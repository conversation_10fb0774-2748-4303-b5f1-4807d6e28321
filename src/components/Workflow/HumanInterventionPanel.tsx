/**
 * Human Intervention Panel Component
 * 
 * Provides interface for humans to intervene in agent collaboration sessions
 * with feedback, requirements, and conflict resolution capabilities.
 */

import React, { useState } from 'react';
import { CollaborationResult } from '../../core/agents/AgentCollaborationEngine';

export interface HumanFeedback {
  type: 'requirement' | 'feedback' | 'conflict-resolution' | 'quality-concern';
  content: string;
  targetAgents?: string[];
  priority: 'low' | 'medium' | 'high';
  timestamp: string;
}

export interface InterventionAction {
  type: 'add-requirement' | 'provide-feedback' | 'resolve-conflict' | 'pause-collaboration' | 'resume-collaboration';
  data?: any;
}

export interface HumanInterventionPanelProps {
  collaborationResult: CollaborationResult;
  isCollaborationActive: boolean;
  onIntervention: (action: InterventionAction) => void;
  onFeedbackSubmit: (feedback: HumanFeedback) => void;
}

export const HumanInterventionPanel: React.FC<HumanInterventionPanelProps> = ({
  collaborationResult,
  isCollaborationActive,
  onIntervention,
  onFeedbackSubmit
}) => {
  const [activeTab, setActiveTab] = useState<'feedback' | 'requirements' | 'conflicts'>('feedback');
  const [feedbackText, setFeedbackText] = useState('');
  const [requirementText, setRequirementText] = useState('');
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  const [priority, setPriority] = useState<'low' | 'medium' | 'high'>('medium');

  const availableAgents = collaborationResult.session.agents;

  const handleSubmitFeedback = () => {
    if (!feedbackText.trim()) return;

    const feedback: HumanFeedback = {
      type: 'feedback',
      content: feedbackText,
      targetAgents: selectedAgents.length > 0 ? selectedAgents : undefined,
      priority,
      timestamp: new Date().toISOString()
    };

    onFeedbackSubmit(feedback);
    setFeedbackText('');
    setSelectedAgents([]);
  };

  const handleSubmitRequirement = () => {
    if (!requirementText.trim()) return;

    const requirement: HumanFeedback = {
      type: 'requirement',
      content: requirementText,
      targetAgents: selectedAgents.length > 0 ? selectedAgents : undefined,
      priority,
      timestamp: new Date().toISOString()
    };

    onFeedbackSubmit(requirement);
    onIntervention({ type: 'add-requirement', data: requirement });
    setRequirementText('');
    setSelectedAgents([]);
  };

  const handlePauseResume = () => {
    if (isCollaborationActive) {
      onIntervention({ type: 'pause-collaboration' });
    } else {
      onIntervention({ type: 'resume-collaboration' });
    }
  };

  const toggleAgentSelection = (agentId: string) => {
    setSelectedAgents(prev => 
      prev.includes(agentId) 
        ? prev.filter(id => id !== agentId)
        : [...prev, agentId]
    );
  };

  const getConsensusStatus = () => {
    const confidence = collaborationResult.consensus.confidence;
    if (confidence >= 0.8) return { status: 'high', color: 'text-green-600', label: 'Strong Consensus' };
    if (confidence >= 0.6) return { status: 'medium', color: 'text-yellow-600', label: 'Moderate Consensus' };
    return { status: 'low', color: 'text-red-600', label: 'Weak Consensus' };
  };

  const consensusStatus = getConsensusStatus();

  return (
    <div className="human-intervention-panel bg-white rounded-lg shadow-lg p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-xl font-semibold text-gray-800">
          👤 Human Intervention Panel
        </h3>
        <div className="flex items-center space-x-4">
          <div className={`text-sm font-medium ${consensusStatus.color}`}>
            {consensusStatus.label} ({Math.round(collaborationResult.consensus.confidence * 100)}%)
          </div>
          <button
            onClick={handlePauseResume}
            className={`px-4 py-2 rounded-md text-sm font-medium ${
              isCollaborationActive 
                ? 'bg-red-500 text-white hover:bg-red-600' 
                : 'bg-green-500 text-white hover:bg-green-600'
            }`}
            data-testid="pause-resume-button"
          >
            {isCollaborationActive ? '⏸️ Pause' : '▶️ Resume'}
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="tab-navigation mb-6">
        <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
          {[
            { id: 'feedback', label: '💬 Feedback', icon: '💬' },
            { id: 'requirements', label: '📋 Requirements', icon: '📋' },
            { id: 'conflicts', label: '⚠️ Conflicts', icon: '⚠️' }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={`flex-1 px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                activeTab === tab.id
                  ? 'bg-white text-blue-600 shadow-sm'
                  : 'text-gray-600 hover:text-gray-800'
              }`}
              data-testid={`tab-${tab.id}`}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTab === 'feedback' && (
          <div className="feedback-tab space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Provide Feedback to Agents
              </label>
              <textarea
                value={feedbackText}
                onChange={(e) => setFeedbackText(e.target.value)}
                placeholder="Share your thoughts on the collaboration progress, quality, or direction..."
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={4}
                data-testid="feedback-textarea"
              />
            </div>

            <div className="agent-selection">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target Specific Agents (optional)
              </label>
              <div className="flex flex-wrap gap-2">
                {availableAgents.map(agentId => (
                  <button
                    key={agentId}
                    onClick={() => toggleAgentSelection(agentId)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      selectedAgents.includes(agentId)
                        ? 'bg-blue-500 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                    data-testid={`agent-select-${agentId}`}
                  >
                    {agentId}
                  </button>
                ))}
              </div>
            </div>

            <div className="priority-selection">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Priority Level
              </label>
              <select
                value={priority}
                onChange={(e) => setPriority(e.target.value as any)}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                data-testid="priority-select"
              >
                <option value="low">Low Priority</option>
                <option value="medium">Medium Priority</option>
                <option value="high">High Priority</option>
              </select>
            </div>

            <button
              onClick={handleSubmitFeedback}
              disabled={!feedbackText.trim()}
              className="w-full px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              data-testid="submit-feedback-button"
            >
              Submit Feedback
            </button>
          </div>
        )}

        {activeTab === 'requirements' && (
          <div className="requirements-tab space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Add New Requirement
              </label>
              <textarea
                value={requirementText}
                onChange={(e) => setRequirementText(e.target.value)}
                placeholder="Specify additional requirements or constraints for the collaboration..."
                className="w-full p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={4}
                data-testid="requirement-textarea"
              />
            </div>

            <div className="agent-selection">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Target Specific Agents (optional)
              </label>
              <div className="flex flex-wrap gap-2">
                {availableAgents.map(agentId => (
                  <button
                    key={agentId}
                    onClick={() => toggleAgentSelection(agentId)}
                    className={`px-3 py-1 rounded-full text-sm font-medium transition-colors ${
                      selectedAgents.includes(agentId)
                        ? 'bg-green-500 text-white'
                        : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                    }`}
                  >
                    {agentId}
                  </button>
                ))}
              </div>
            </div>

            <button
              onClick={handleSubmitRequirement}
              disabled={!requirementText.trim()}
              className="w-full px-4 py-2 bg-green-500 text-white rounded-md hover:bg-green-600 disabled:bg-gray-300 disabled:cursor-not-allowed"
              data-testid="submit-requirement-button"
            >
              Add Requirement
            </button>
          </div>
        )}

        {activeTab === 'conflicts' && (
          <div className="conflicts-tab space-y-4">
            <div className="conflict-detection">
              <h4 className="text-lg font-medium text-gray-800 mb-3">Detected Conflicts</h4>
              
              {collaborationResult.consensus.disagreements.length > 0 ? (
                <div className="space-y-3">
                  {collaborationResult.consensus.disagreements.map((disagreement, index) => (
                    <div key={index} className="bg-red-50 border border-red-200 rounded-lg p-3">
                      <div className="flex items-start justify-between">
                        <span className="text-sm text-red-800">{disagreement}</span>
                        <button
                          onClick={() => onIntervention({ 
                            type: 'resolve-conflict', 
                            data: { disagreement, index } 
                          })}
                          className="px-3 py-1 bg-red-500 text-white text-xs rounded-md hover:bg-red-600"
                          data-testid={`resolve-conflict-${index}`}
                        >
                          Resolve
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <div className="text-4xl mb-2">✅</div>
                  <div>No conflicts detected</div>
                  <div className="text-sm">Agents are in good alignment</div>
                </div>
              )}
            </div>

            <div className="consensus-breakdown">
              <h4 className="text-lg font-medium text-gray-800 mb-3">Consensus Breakdown</h4>
              <div className="space-y-2">
                {collaborationResult.consensus.agreements.map((agreement, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <span className="text-green-500">✓</span>
                    <span className="text-gray-700">{agreement}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
