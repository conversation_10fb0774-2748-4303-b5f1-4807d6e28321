/**
 * UI Integration Example
 * 
 * Comprehensive example showing how to integrate all the collaboration UI components:
 * - AgentCollaborationViewer
 * - RealTimeCollaborationMonitor  
 * - HumanInterventionPanel
 * - DynamicWorkflowExecution
 */

import React, { useState, useEffect } from 'react';
import { AgentCollaborationViewer, HumanInteraction } from '../components/Workflow/AgentCollaborationViewer';
import { RealTimeCollaborationMonitor, CollaborationUpdate, AgentActivity } from '../components/Workflow/RealTimeCollaborationMonitor';
import { HumanInterventionPanel, HumanFeedback, InterventionAction } from '../components/Workflow/HumanInterventionPanel';
import { AgentCollaborationEngine, CollaborationResult } from '../core/agents/AgentCollaborationEngine';
import { DynamicWorkflowExecution } from '../components/Workflow/DynamicWorkflowExecution';
import { SeoKeywordAgent } from '../core/agents/seo-keyword-agent';
import { MarketResearchAgent } from '../core/agents/market-research-agent';
import { ContentStrategyAgent } from '../core/agents/content-strategy-agent';

// Mock data for demonstration
const mockCollaborationResult: CollaborationResult = {
  artifact: {
    id: 'demo-artifact',
    type: 'content',
    content: 'AI Startups in 2025: A comprehensive guide to emerging trends and investment opportunities in the artificial intelligence startup ecosystem.',
    metadata: {
      collaborationComplete: true,
      consensusConfidence: 0.88,
      qualityScore: 88
    }
  },
  consensus: {
    confidence: 0.88,
    agreements: [
      'Strong market demand for AI startup content',
      'SEO optimization is crucial for visibility',
      'Content structure should focus on actionable insights'
    ],
    disagreements: [
      'Technical depth vs. accessibility balance'
    ],
    finalRecommendations: [
      'Use "AI startups 2025" as primary keyword',
      'Target entrepreneurs and investors',
      'Include market size data and growth projections',
      'Add case studies of successful AI startups'
    ],
    qualityScore: 88
  },
  rounds: [
    {
      number: 1,
      agentInputs: new Map([
        ['seo-keyword', {
          agentId: 'seo-keyword',
          roundNumber: 1,
          analysis: { keywords: ['AI startups', '2025', 'artificial intelligence'], searchVolume: 'high' },
          suggestions: ['Use primary keyword in title', 'Optimize meta description', 'Include long-tail keywords'],
          confidence: 0.85,
          reasoning: 'Strong SEO potential with high search volume for AI startup topics'
        }],
        ['market-research', {
          agentId: 'market-research',
          roundNumber: 1,
          analysis: { marketSize: '$50B by 2025', growth: '25% CAGR', targetAudience: 'entrepreneurs, investors' },
          suggestions: ['Focus on market size data', 'Include investment trends', 'Target B2B audience'],
          confidence: 0.82,
          reasoning: 'Market research shows strong growth in AI startup sector'
        }]
      ]),
      peerReviews: new Map(),
      synthesizedResult: {
        content: 'Enhanced content with SEO and market insights',
        confidence: 0.835
      }
    },
    {
      number: 2,
      agentInputs: new Map([
        ['content-strategy', {
          agentId: 'content-strategy',
          roundNumber: 2,
          analysis: { structure: 'optimized', readability: 'high', engagement: 'strong' },
          suggestions: ['Add executive summary', 'Include visual elements', 'Create actionable takeaways'],
          confidence: 0.9,
          reasoning: 'Content strategy aligns with audience needs and business objectives'
        }]
      ]),
      peerReviews: new Map(),
      synthesizedResult: {
        content: 'Final enhanced content with comprehensive agent collaboration',
        confidence: 0.88
      }
    }
  ],
  session: {
    id: 'demo-session-123',
    task: {
      type: 'artifact-refinement',
      stepId: 'content-creation',
      stepType: 'content-creation',
      objective: 'Create high-quality AI startup content through multi-agent collaboration'
    },
    agents: ['seo-keyword', 'market-research', 'content-strategy'],
    startedAt: '2024-01-15T10:00:00Z',
    status: 'completed'
  }
};

export const UIIntegrationExample: React.FC = () => {
  const [selectedRound, setSelectedRound] = useState(0);
  const [isCollaborationActive, setIsCollaborationActive] = useState(false);
  const [agentActivities, setAgentActivities] = useState<AgentActivity[]>([
    { agentId: 'seo-keyword', status: 'completed', lastSeen: '2024-01-15T10:15:00Z' },
    { agentId: 'market-research', status: 'completed', lastSeen: '2024-01-15T10:14:00Z' },
    { agentId: 'content-strategy', status: 'completed', lastSeen: '2024-01-15T10:16:00Z' }
  ]);
  const [collaborationResult, setCollaborationResult] = useState(mockCollaborationResult);
  const [feedbackHistory, setFeedbackHistory] = useState<HumanFeedback[]>([]);

  // Initialize collaboration engine
  const [collaborationEngine] = useState(() => {
    const engine = new AgentCollaborationEngine();
    engine.registerAgent(new SeoKeywordAgent());
    engine.registerAgent(new MarketResearchAgent());
    engine.registerAgent(new ContentStrategyAgent());
    return engine;
  });

  const [dynamicExecution] = useState(() => new DynamicWorkflowExecution(collaborationEngine));

  // Handle collaboration viewer interactions
  const handleCollaborationInteraction = (interaction: HumanInteraction) => {
    console.log('🎯 Collaboration interaction:', interaction);
    
    switch (interaction.type) {
      case 'round-select':
        if (interaction.roundNumber !== undefined) {
          setSelectedRound(interaction.roundNumber - 1); // Convert to 0-based index
        }
        break;
      case 'add-requirement':
        // Handle adding requirements
        console.log('📋 Adding requirement');
        break;
      case 'provide-feedback':
        // Handle providing feedback
        console.log('💬 Providing feedback');
        break;
      case 'resolve-conflict':
        // Handle conflict resolution
        console.log('⚠️ Resolving conflict');
        break;
    }
  };

  // Handle real-time collaboration updates
  const handleSessionUpdate = (update: CollaborationUpdate) => {
    console.log('📡 Session update:', update);
    
    switch (update.type) {
      case 'round-started':
        setIsCollaborationActive(true);
        // Update agent activities to show they're active
        setAgentActivities(prev => prev.map(activity => ({
          ...activity,
          status: update.data.participatingAgents.includes(activity.agentId) ? 'analyzing' : 'waiting'
        })));
        break;
      case 'round-completed':
        // Update collaboration result with new round data
        console.log('Round completed:', update.data);
        break;
      case 'consensus-updated':
        // Update consensus information
        console.log('Consensus updated:', update.data);
        break;
    }
  };

  // Handle human intervention actions
  const handleIntervention = (action: InterventionAction) => {
    console.log('👤 Human intervention:', action);
    
    switch (action.type) {
      case 'pause-collaboration':
        setIsCollaborationActive(false);
        setAgentActivities(prev => prev.map(activity => ({
          ...activity,
          status: 'waiting'
        })));
        break;
      case 'resume-collaboration':
        setIsCollaborationActive(true);
        setAgentActivities(prev => prev.map(activity => ({
          ...activity,
          status: 'analyzing'
        })));
        break;
      case 'add-requirement':
        console.log('📋 Requirement added:', action.data);
        break;
      case 'resolve-conflict':
        console.log('⚠️ Conflict resolved:', action.data);
        break;
    }
  };

  // Handle feedback submission
  const handleFeedbackSubmit = (feedback: HumanFeedback) => {
    console.log('💬 Feedback submitted:', feedback);
    setFeedbackHistory(prev => [...prev, feedback]);
    
    // Simulate agent response to feedback
    setTimeout(() => {
      setAgentActivities(prev => prev.map(activity => ({
        ...activity,
        status: feedback.targetAgents?.includes(activity.agentId) ? 'responding' : activity.status,
        lastSeen: new Date().toISOString()
      })));
    }, 1000);
  };

  // Simulate starting a new collaboration
  const handleStartNewCollaboration = async () => {
    console.log('🚀 Starting new collaboration...');
    setIsCollaborationActive(true);
    
    // Reset agent activities
    setAgentActivities([
      { agentId: 'seo-keyword', status: 'analyzing', lastSeen: new Date().toISOString() },
      { agentId: 'market-research', status: 'analyzing', lastSeen: new Date().toISOString() },
      { agentId: 'content-strategy', status: 'waiting', lastSeen: new Date().toISOString() }
    ]);

    try {
      // Execute a workflow step with collaboration
      const step = {
        id: 'demo-content-creation',
        name: 'Content Creation',
        type: 'content-creation'
      };

      const inputs = {
        topic: 'AI Startups 2025: Future Trends',
        targetAudience: 'entrepreneurs and investors',
        primaryKeyword: 'AI startups 2025'
      };

      const result = await dynamicExecution.executeStepWithCollaboration(step, inputs);
      
      if (result.collaboration) {
        setCollaborationResult(result.collaboration);
        setIsCollaborationActive(false);
        setAgentActivities(prev => prev.map(activity => ({
          ...activity,
          status: 'completed',
          lastSeen: new Date().toISOString()
        })));
      }
    } catch (error) {
      console.error('❌ Collaboration failed:', error);
      setIsCollaborationActive(false);
    }
  };

  return (
    <div className="ui-integration-example min-h-screen bg-gray-100 p-6">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg shadow-lg p-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-800">
                🤖 Dynamic Agent Collaboration Dashboard
              </h1>
              <p className="text-gray-600 mt-2">
                Real-time monitoring and interaction with multi-agent collaboration sessions
              </p>
            </div>
            <button
              onClick={handleStartNewCollaboration}
              disabled={isCollaborationActive}
              className="px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-400 disabled:cursor-not-allowed font-medium"
            >
              {isCollaborationActive ? '🔄 Collaborating...' : '🚀 Start New Collaboration'}
            </button>
          </div>
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          {/* Left Column */}
          <div className="space-y-6">
            {/* Real-time Monitor */}
            <RealTimeCollaborationMonitor
              sessionId={collaborationResult.session.id}
              activeSessions={[collaborationResult.session]}
              agentActivities={agentActivities}
              metrics={{
                averageResponseTime: 2.3,
                consensusVelocity: 0.15,
                agentParticipation: 0.95,
                qualityScore: collaborationResult.consensus.qualityScore
              }}
              progressData={{
                currentRound: selectedRound + 1,
                totalRounds: collaborationResult.rounds.length,
                consensusLevel: collaborationResult.consensus.confidence,
                participatingAgents: collaborationResult.session.agents.length
              }}
              alerts={collaborationResult.consensus.disagreements.map((disagreement, index) => ({
                id: `alert-${index}`,
                type: 'consensus-stalled',
                message: disagreement,
                severity: 'warning',
                timestamp: new Date().toISOString()
              }))}
              onSessionUpdate={handleSessionUpdate}
            />

            {/* Human Intervention Panel */}
            <HumanInterventionPanel
              collaborationResult={collaborationResult}
              isCollaborationActive={isCollaborationActive}
              onIntervention={handleIntervention}
              onFeedbackSubmit={handleFeedbackSubmit}
            />
          </div>

          {/* Right Column */}
          <div className="space-y-6">
            {/* Collaboration Viewer */}
            <AgentCollaborationViewer
              collaborationResult={collaborationResult}
              activeRound={selectedRound}
              isLive={isCollaborationActive}
              onInteraction={handleCollaborationInteraction}
              onRoundSelect={handleCollaborationInteraction}
            />

            {/* Feedback History */}
            {feedbackHistory.length > 0 && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <h3 className="text-lg font-semibold text-gray-800 mb-4">
                  📝 Feedback History
                </h3>
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {feedbackHistory.map((feedback, index) => (
                    <div key={index} className="bg-gray-50 rounded-lg p-3 border border-gray-200">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="text-sm font-medium text-gray-800">
                            {feedback.type === 'requirement' ? '📋' : '💬'} {feedback.type}
                          </div>
                          <div className="text-sm text-gray-600 mt-1">
                            {feedback.content}
                          </div>
                          {feedback.targetAgents && (
                            <div className="text-xs text-gray-500 mt-1">
                              Target: {feedback.targetAgents.join(', ')}
                            </div>
                          )}
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          feedback.priority === 'high' ? 'bg-red-100 text-red-800' :
                          feedback.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {feedback.priority}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};
