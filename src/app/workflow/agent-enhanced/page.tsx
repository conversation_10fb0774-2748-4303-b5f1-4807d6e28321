/**
 * Advanced Agent-Enhanced Workflow Dashboard
 *
 * Showcases the latest UIIntegrationExample with dynamic agent collaboration,
 * conflict resolution, analytics, learning engine, and real-time monitoring
 */

'use client';

import { useState } from 'react';
import { UIIntegrationExample } from '../../../examples/UIIntegrationExample';

type ViewMode = 'overview' | 'demo' | 'features' | 'documentation';

export default function AgentEnhancedWorkflowPage() {
  const [activeView, setActiveView] = useState<ViewMode>('overview');

  const getViewIcon = (view: ViewMode) => {
    switch (view) {
      case 'overview': return '🏠';
      case 'demo': return '🚀';
      case 'features': return '⚡';
      case 'documentation': return '📚';
      default: return '📋';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Advanced Agent Collaboration System</h1>
              <p className="text-sm text-gray-600">
                Dynamic agent collaboration with conflict resolution, analytics, and learning
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-gray-700">All Systems Operational</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {(['overview', 'demo', 'features', 'documentation'] as ViewMode[]).map((view) => (
              <button
                key={view}
                onClick={() => setActiveView(view)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeView === view
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {getViewIcon(view)} {view.charAt(0).toUpperCase() + view.slice(1)}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {activeView === 'overview' && (
          <div className="space-y-6">
            {/* System Overview */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-4">🚀 System Overview</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">🤖</span>
                    <h3 className="font-medium text-blue-900">Agent Collaboration</h3>
                  </div>
                  <p className="text-sm text-blue-700">
                    Dynamic multi-agent collaboration with real-time communication and consensus building
                  </p>
                </div>

                <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">⚔️</span>
                    <h3 className="font-medium text-red-900">Conflict Resolution</h3>
                  </div>
                  <p className="text-sm text-red-700">
                    Automatic detection and resolution of agent disagreements using semantic analysis
                  </p>
                </div>

                <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">📊</span>
                    <h3 className="font-medium text-green-900">Analytics Engine</h3>
                  </div>
                  <p className="text-sm text-green-700">
                    Comprehensive analytics with performance tracking and quality metrics
                  </p>
                </div>

                <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-2xl">🧠</span>
                    <h3 className="font-medium text-purple-900">Learning Engine</h3>
                  </div>
                  <p className="text-sm text-purple-700">
                    Machine learning system that improves collaboration patterns over time
                  </p>
                </div>
              </div>
            </div>

            {/* Quick Start */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">🎯 Quick Start</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 text-xl">1️⃣</span>
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">Try the Demo</h4>
                  <p className="text-sm text-gray-600">Experience the full collaboration system in action</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <span className="text-green-600 text-xl">2️⃣</span>
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">Explore Features</h4>
                  <p className="text-sm text-gray-600">Learn about advanced capabilities and integrations</p>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mx-auto mb-3">
                    <span className="text-purple-600 text-xl">3️⃣</span>
                  </div>
                  <h4 className="font-medium text-gray-900 mb-2">Read Documentation</h4>
                  <p className="text-sm text-gray-600">Understand the technical implementation details</p>
                </div>
              </div>
              <div className="mt-6 text-center">
                <button
                  onClick={() => setActiveView('demo')}
                  className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
                >
                  🚀 Start Interactive Demo
                </button>
              </div>
            </div>
          </div>
        )}

        {activeView === 'demo' && (
          <div className="space-y-6">
            {/* Demo Header */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h2 className="text-xl font-semibold text-gray-900">🚀 Interactive Demo</h2>
                  <p className="text-gray-600 mt-1">
                    Experience the complete agent collaboration system with all functional features
                  </p>
                </div>
                <div className="flex items-center space-x-2 text-sm">
                  <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  <span className="text-green-700 font-medium">Live Demo Active</span>
                </div>
              </div>

              {/* Feature Highlights */}
              <div className="grid grid-cols-1 md:grid-cols-4 gap-3 mb-6">
                <div className="bg-blue-50 px-3 py-2 rounded-lg border border-blue-200">
                  <div className="text-xs font-medium text-blue-800">🔍 Conflict Detection</div>
                  <div className="text-xs text-blue-600">Real-time conflict monitoring</div>
                </div>
                <div className="bg-green-50 px-3 py-2 rounded-lg border border-green-200">
                  <div className="text-xs font-medium text-green-800">📊 Live Analytics</div>
                  <div className="text-xs text-green-600">Performance tracking</div>
                </div>
                <div className="bg-purple-50 px-3 py-2 rounded-lg border border-purple-200">
                  <div className="text-xs font-medium text-purple-800">🧠 Learning Engine</div>
                  <div className="text-xs text-purple-600">Pattern recognition</div>
                </div>
                <div className="bg-orange-50 px-3 py-2 rounded-lg border border-orange-200">
                  <div className="text-xs font-medium text-orange-800">⚡ Real-time UI</div>
                  <div className="text-xs text-orange-600">Dynamic updates</div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-semibold text-yellow-900 mb-2">💡 Demo Instructions</h4>
                <ul className="text-sm text-yellow-800 space-y-1">
                  <li>• Click "Start New Collaboration" to begin an agent collaboration session</li>
                  <li>• Watch real-time agent communication and conflict resolution</li>
                  <li>• Monitor analytics and learning insights in the functional features panel</li>
                  <li>• Provide human feedback to see dynamic agent consultation in action</li>
                </ul>
              </div>
            </div>

            {/* UIIntegrationExample Demo */}
            <div className="bg-white rounded-lg shadow-lg overflow-hidden">
              <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
                <h3 className="text-lg font-semibold text-white">
                  🎯 Live Agent Collaboration System
                </h3>
                <p className="text-blue-100 text-sm mt-1">
                  Complete integration of conflict resolution, analytics, and learning features
                </p>
              </div>

              <div className="p-0">
                <UIIntegrationExample />
              </div>
            </div>
          </div>
        )}

        {activeView === 'features' && (
          <div className="space-y-6">
            {/* Features Overview */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">⚡ Advanced Features</h2>

              <div className="space-y-6">
                {/* Conflict Resolution */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                      <span className="text-red-600 text-xl">⚔️</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Conflict Detection & Resolution</h3>
                      <p className="text-sm text-gray-600">Advanced semantic analysis for agent disagreements</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Detection Capabilities</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Semantic conflict analysis using NLP</li>
                        <li>• Priority-based disagreement detection</li>
                        <li>• Approach methodology conflicts</li>
                        <li>• Factual contradiction identification</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Resolution Strategies</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Automatic semantic merging</li>
                        <li>• Confidence-weighted prioritization</li>
                        <li>• Compromise solution generation</li>
                        <li>• Human escalation when needed</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Analytics Engine */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                      <span className="text-green-600 text-xl">📊</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Analytics & Performance Tracking</h3>
                      <p className="text-sm text-gray-600">Comprehensive metrics and insights</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Performance Metrics</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Response time tracking</li>
                        <li>• Throughput analysis</li>
                        <li>• Error rate monitoring</li>
                        <li>• Resource utilization</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Quality Analysis</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Collaboration quality scores</li>
                        <li>• Consensus confidence tracking</li>
                        <li>• Trend analysis over time</li>
                        <li>• Anomaly detection</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Reporting</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Executive summaries</li>
                        <li>• Detailed analytics reports</li>
                        <li>• Export capabilities (JSON, CSV)</li>
                        <li>• Real-time dashboards</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* Learning Engine */}
                <div className="border border-gray-200 rounded-lg p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                      <span className="text-purple-600 text-xl">🧠</span>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">Collaboration Learning Engine</h3>
                      <p className="text-sm text-gray-600">Machine learning for continuous improvement</p>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Pattern Recognition</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Agent performance patterns</li>
                        <li>• Optimal agent combinations</li>
                        <li>• Quality prediction models</li>
                        <li>• Success factor analysis</li>
                      </ul>
                    </div>
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Optimization</h4>
                      <ul className="text-sm text-gray-600 space-y-1">
                        <li>• Automatic agent selection</li>
                        <li>• Collaboration outcome prediction</li>
                        <li>• Performance recommendations</li>
                        <li>• Continuous model improvement</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeView === 'documentation' && (
          <div className="space-y-6">
            {/* Documentation */}
            <div className="bg-white rounded-lg shadow-lg p-6">
              <h2 className="text-xl font-semibold text-gray-900 mb-6">📚 Technical Documentation</h2>

              <div className="space-y-6">
                {/* Architecture Overview */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">🏗️ System Architecture</h3>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <pre className="text-sm text-gray-700 overflow-x-auto">
{`src/core/
├── collaboration/
│   ├── ConflictDetector.ts      # Semantic conflict detection
│   └── ConflictResolver.ts      # Multi-strategy resolution
├── analytics/
│   ├── DataCollector.ts         # Collaboration data collection
│   ├── MetricsCalculator.ts     # Performance metrics
│   ├── TrendAnalyzer.ts         # Pattern analysis
│   ├── ReportGenerator.ts       # Report generation
│   └── AnalyticsEngine.ts       # Main analytics engine
├── learning/
│   └── CollaborationLearningEngine.ts  # ML-based optimization
└── agents/
    ├── AgentCollaborationEngine.ts     # Core collaboration
    └── types.ts                        # Type definitions`}
                    </pre>
                  </div>
                </div>

                {/* Implementation Details */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">⚙️ Implementation Details</h3>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                      <h4 className="font-medium text-blue-900 mb-2">Conflict Detection Algorithm</h4>
                      <p className="text-sm text-blue-700 mb-2">
                        Uses semantic similarity analysis to detect conflicts between agent suggestions:
                      </p>
                      <ul className="text-xs text-blue-600 space-y-1">
                        <li>• NLP-based semantic comparison</li>
                        <li>• Confidence threshold analysis</li>
                        <li>• Pattern-based conflict recognition</li>
                        <li>• Multi-dimensional conflict scoring</li>
                      </ul>
                    </div>

                    <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                      <h4 className="font-medium text-green-900 mb-2">Analytics Pipeline</h4>
                      <p className="text-sm text-green-700 mb-2">
                        Real-time data collection and analysis pipeline:
                      </p>
                      <ul className="text-xs text-green-600 space-y-1">
                        <li>• Event-driven data collection</li>
                        <li>• Streaming analytics processing</li>
                        <li>• Time-series trend analysis</li>
                        <li>• Predictive quality modeling</li>
                      </ul>
                    </div>
                  </div>
                </div>

                {/* API Reference */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3">🔌 Key APIs</h3>
                  <div className="space-y-4">
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">ConflictDetector</h4>
                      <code className="text-sm text-gray-700">
                        detectConflicts(agentInputs: AgentInput[]): Promise&lt;ConflictDetectionResult&gt;
                      </code>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">AnalyticsEngine</h4>
                      <code className="text-sm text-gray-700">
                        generatePerformanceReport(timeRange: TimeRange): Promise&lt;AnalyticsReport&gt;
                      </code>
                    </div>
                    <div className="bg-gray-50 rounded-lg p-4">
                      <h4 className="font-medium text-gray-900 mb-2">CollaborationLearningEngine</h4>
                      <code className="text-sm text-gray-700">
                        optimizeAgentSelection(context: CollaborationContext): Promise&lt;string[]&gt;
                      </code>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Agent-Enhanced Workflows</h1>
              <p className="text-sm text-gray-600">
                Create intelligent workflows with dynamic agent consultation
              </p>
            </div>

            <div className="flex items-center space-x-4">
              {/* Agent Status Indicator */}
              {agentMetrics && (
                <div className="flex items-center space-x-2 text-sm">
                  <div className={`w-3 h-3 rounded-full ${
                    agentMetrics.successRate > 0.9 ? 'bg-green-500' :
                    agentMetrics.successRate > 0.7 ? 'bg-yellow-500' : 'bg-red-500'
                  }`}></div>
                  <span className="text-gray-700">
                    Agents: {Math.round(agentMetrics.successRate * 100)}% success
                  </span>
                </div>
              )}

              {/* Current Execution */}
              {currentExecution && (
                <div className="text-sm text-blue-600">
                  Active: <span className="font-medium">{currentExecution.slice(-8)}</span>
                </div>
              )}

              {/* Quick Actions */}
              <button
                onClick={() => setActiveTab('agents')}
                className="flex items-center px-3 py-2 text-sm text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-colors"
              >
                <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v4" />
                </svg>
                Agent Dashboard
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Notifications */}
      {notifications.length > 0 && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
          <div className="space-y-2">
            {notifications.map((notification, index) => (
              <div
                key={index}
                className="bg-blue-50 border border-blue-200 rounded-lg p-3 text-sm text-blue-800 animate-fade-in"
              >
                🔔 {notification}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Tab Navigation */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {(['templates', 'builder', 'agents', 'execution', 'monitor', 'feedback'] as TabType[]).map((tab) => {
              const badge = getTabBadge(tab);
              return (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <span>{getTabIcon(tab)} {tab.charAt(0).toUpperCase() + tab.slice(1)}</span>
                  {badge && (
                    <span className={`inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full ${
                      tab === 'monitor' ? 'text-green-100 bg-green-600' : 'text-blue-100 bg-blue-600'
                    }`}>
                      {badge}
                    </span>
                  )}
                </button>
              );
            })}
          </nav>
        </div>
      </div>

      {/* Tab Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {activeTab === 'templates' && (
          <div className="space-y-6">
            <div className="bg-white rounded-lg shadow-lg p-6">
              <div className="mb-6">
                <h2 className="text-xl font-semibold text-gray-900 mb-2">Choose a Workflow Template</h2>
                <p className="text-gray-600">
                  Select from our collection of pre-built templates with intelligent agent consultation
                </p>
              </div>
              
              <TemplateSelector
                onTemplateSelect={handleTemplateSelect}
                showAgentFeatures={true}
                filterByAgentSupport={false}
              />
            </div>

            {/* Agent-Enhanced Templates Showcase */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg p-6 border border-blue-200">
              <div className="flex items-center space-x-3 mb-4">
                <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                  <span className="text-blue-600 text-xl">🤖</span>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900">Agent-Enhanced Templates</h3>
                  <p className="text-sm text-gray-600">Templates with built-in intelligent agent consultation</p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="bg-white rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">🔍</span>
                    <h4 className="font-medium text-gray-900">SEO Blog Post</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Automatic keyword research and SEO optimization with agent consultation
                  </p>
                  <div className="flex items-center space-x-2 text-xs">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">SEO Agent</span>
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded">Strategy Agent</span>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">📊</span>
                    <h4 className="font-medium text-gray-900">Market Research</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Content creation with market insights and competitive analysis
                  </p>
                  <div className="flex items-center space-x-2 text-xs">
                    <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">Market Agent</span>
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded">Strategy Agent</span>
                  </div>
                </div>

                <div className="bg-white rounded-lg p-4 border border-blue-200">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="text-lg">📝</span>
                    <h4 className="font-medium text-gray-900">Content Strategy</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Strategic content planning with multi-agent collaboration
                  </p>
                  <div className="flex items-center space-x-2 text-xs">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded">SEO Agent</span>
                    <span className="bg-purple-100 text-purple-800 px-2 py-1 rounded">Market Agent</span>
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded">Strategy Agent</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'builder' && (
          <div className="bg-white rounded-lg shadow-lg">
            <WorkflowBuilder
              selectedTemplate={selectedTemplate}
              onWorkflowCreate={handleWorkflowCreate}
              enableAgentConsultation={true}
              onNotification={addNotification}
            />
          </div>
        )}

        {activeTab === 'agents' && (
          <div className="space-y-6">
            <AgentConsultationConfig
              onConfigUpdate={(config) => {
                addNotification('Agent consultation configuration updated');
              }}
              onNotification={addNotification}
            />
          </div>
        )}

        {activeTab === 'execution' && (
          <div className="bg-white rounded-lg shadow-lg">
            <WorkflowExecution
              workflow={currentWorkflow}
              onExecutionStart={handleExecutionStart}
              onNotification={addNotification}
              enableAgentConsultation={true}
            />
          </div>
        )}

        {activeTab === 'monitor' && (
          <div className="space-y-6">
            <AgentActivityMonitor
              executionId={currentExecution}
              metrics={agentMetrics}
              onNotification={addNotification}
            />

            {/* Quick Artifacts Access */}
            {currentExecution && (
              <div className="bg-white rounded-lg shadow-lg p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-gray-900">Generated Artifacts</h3>
                  <button
                    onClick={() => {
                      window.open(`/workflow/agent-enhanced/artifacts/${currentExecution}`, '_blank');
                    }}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    📄 View All Artifacts
                  </button>
                </div>
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-900 mb-2">🎯 Execution Information</h4>
                  <div className="text-sm text-blue-800 space-y-1">
                    <p><strong>Current Execution ID:</strong> <code>{currentExecution}</code></p>
                    <p><strong>ID Format:</strong> {currentExecution.startsWith('exec-') ? 'WorkflowExecution (exec-*)' : 'Workflow Engine (UUID)'}</p>
                    <p><strong>System:</strong> Agent-Enhanced Workflow</p>
                  </div>
                  <div className="mt-3 text-sm text-blue-800">
                    <p><strong>Available Actions:</strong></p>
                    <ul className="list-disc list-inside space-y-1 ml-4">
                      <li>Click "View All Artifacts" to see generated content</li>
                      <li>Monitor agent consultations in the activity log above</li>
                      <li>Use "Feedback Tab" to review and approve artifacts</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}

        {activeTab === 'feedback' && (
          <div className="space-y-6">
            {/* Debug Controls */}
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <h3 className="text-lg font-medium text-yellow-900 mb-3">🔧 Debug Controls</h3>
              <div className="flex space-x-3">
                <button
                  onClick={async () => {
                    try {
                      const response = await fetch('/api/agents/consultation', { method: 'DELETE' });
                      const result = await response.json();
                      if (result.success) {
                        addNotification('Agent consultation counters cleared - infinite loop fixed!');
                      }
                    } catch (error) {
                      addNotification('Failed to clear consultation counters');
                    }
                  }}
                  className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors"
                >
                  🧹 Clear Agent Consultation Counters
                </button>
                <button
                  onClick={() => {
                    window.open(`/workflow/agent-enhanced/artifacts/${currentExecution}`, '_blank');
                  }}
                  disabled={!currentExecution}
                  className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors"
                >
                  📄 View Generated Artifacts
                </button>
              </div>
              <p className="text-sm text-yellow-800 mt-2">
                If you're experiencing infinite agent consultation loops, click "Clear Agent Consultation Counters" to reset the system.
              </p>
            </div>

            <HumanFeedbackInterface
              workflowExecutionId={currentExecution || ''}
              stepId="content-creation"
              artifact={currentArtifact}
              onFeedbackSubmit={(feedback) => {
                addNotification('Feedback submitted and agents consulted');
              }}
              onArtifactApprove={(artifactId) => {
                addNotification('Artifact approved');
                setCurrentArtifact(null);
              }}
              onArtifactReject={(artifactId, feedback) => {
                addNotification('Artifact rejected with feedback');
                // Trigger regeneration workflow
              }}
              onNotification={addNotification}
            />
          </div>
        )}
      </div>
    </div>
  );
}
