/**
 * Functional Article Creation Workflow
 *
 * Complete end-to-end article creation system with agent collaboration,
 * conflict resolution, analytics, and human feedback integration
 */

'use client';

import { useState } from 'react';
import WorkflowInterface from '../../../components/Workflow/WorkflowInterface';

export default function AgentEnhancedWorkflowPage() {
  const [showAdvancedFeatures, setShowAdvancedFeatures] = useState(false);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">AI Article Creation System</h1>
              <p className="text-sm text-gray-600">
                Create high-quality articles with intelligent agent collaboration
              </p>
            </div>

            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2 text-sm">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-gray-700">AI Agents Ready</span>
              </div>

              <button
                onClick={() => setShowAdvancedFeatures(!showAdvancedFeatures)}
                className={`px-3 py-2 text-sm rounded-lg transition-colors ${
                  showAdvancedFeatures
                    ? 'bg-blue-100 text-blue-800 border border-blue-300'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {showAdvancedFeatures ? '🔧 Hide Advanced' : '⚙️ Show Advanced'}
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Advanced Features Panel */}
        {showAdvancedFeatures && (
          <div className="mb-6 bg-white rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold text-gray-900 mb-4">🔧 Advanced Features Status</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xl">🤖</span>
                  <h4 className="font-medium text-blue-900">Agent Collaboration</h4>
                </div>
                <div className="text-xs text-blue-700">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Active</span>
                  </div>
                </div>
              </div>

              <div className="bg-red-50 rounded-lg p-4 border border-red-200">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xl">⚔️</span>
                  <h4 className="font-medium text-red-900">Conflict Resolution</h4>
                </div>
                <div className="text-xs text-red-700">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Ready</span>
                  </div>
                </div>
              </div>

              <div className="bg-green-50 rounded-lg p-4 border border-green-200">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xl">📊</span>
                  <h4 className="font-medium text-green-900">Analytics</h4>
                </div>
                <div className="text-xs text-green-700">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Monitoring</span>
                  </div>
                </div>
              </div>

              <div className="bg-purple-50 rounded-lg p-4 border border-purple-200">
                <div className="flex items-center space-x-2 mb-2">
                  <span className="text-xl">🧠</span>
                  <h4 className="font-medium text-purple-900">Learning Engine</h4>
                </div>
                <div className="text-xs text-purple-700">
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span>Learning</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Workflow Interface */}
        <div className="bg-white rounded-lg shadow-lg overflow-hidden">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-6 py-4">
            <h2 className="text-xl font-semibold text-white">
              📝 Create Your Article
            </h2>
            <p className="text-blue-100 text-sm mt-1">
              Select a template, provide your requirements, and let our AI agents collaborate to create high-quality content
            </p>
          </div>

          <div className="p-0">
            <WorkflowInterface
              onWorkflowComplete={(executionId) => {
                console.log('Workflow completed:', executionId);
              }}
              onWorkflowCreated={(executionId) => {
                console.log('Workflow created:', executionId);
              }}
            />
          </div>
        </div>

      </div>
    </div>
  );
}
