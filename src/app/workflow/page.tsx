/**
 * Modern Article Creation Workflow
 * Redirects to the advanced agent-enhanced workflow system
 */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function WorkflowPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the advanced agent-enhanced workflow
    router.push('/workflow/agent-enhanced');
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Redirecting to Advanced Workflow System</h2>
        <p className="text-gray-600">
          Taking you to the latest agent-enhanced collaboration interface...
        </p>
      </div>
    </div>
  );

