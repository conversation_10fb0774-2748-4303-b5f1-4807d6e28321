/**
 * Modern Article Creation Workflow
 * Redirects to the advanced agent-enhanced workflow system
 */

'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function WorkflowPage() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the advanced agent-enhanced workflow
    router.push('/workflow/agent-enhanced');
  }, [router]);

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 className="text-xl font-semibold text-gray-900 mb-2">Redirecting to Advanced Workflow System</h2>
        <p className="text-gray-600">
          Taking you to the latest agent-enhanced collaboration interface...
        </p>
      </div>
    </div>
  );
}

  const loadTemplates = async () => {
    try {
      const response = await fetch('/api/workflow/create');
      const result = await response.json();

      if (result.success) {
        setTemplates(result.data.templates);
      } else {
        setError('Failed to load templates');
      }
    } catch (err) {
      setError('Failed to load templates');
      console.error(err);
    }
  };

  const selectTemplate = (template: Template) => {
    setSelectedTemplate(template);
    setInputs(template.sampleInputs);
    setError('');
  };

  const updateInput = (key: string, value: string) => {
    setInputs(prev => ({ ...prev, [key]: value }));
  };

  const executeWorkflow = async () => {
    if (!selectedTemplate) return;

    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/workflow/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          templateId: selectedTemplate.id,
          inputs,
          userApiKey: userApiKey || undefined
        })
      });

      const result = await response.json();

      if (result.success) {
        // Start polling for status
        checkExecutionStatus(result.data.executionId);
      } else {
        setError(result.error || 'Failed to execute workflow');
      }
    } catch (err) {
      setError('Failed to execute workflow');
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  const checkExecutionStatus = async (executionId: string) => {
    try {
      const response = await fetch(`/api/workflow/create?executionId=${executionId}`);
      const result = await response.json();

      if (result.success) {
        setExecution(result.data.execution);
      }
    } catch (err) {
      console.error('Failed to check execution status:', err);
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <h1 className="text-3xl font-bold mb-8">Workflow System Test</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
          {error}
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Template Selection */}
        <div>
          <h2 className="text-xl font-semibold mb-4">Available Templates</h2>
          <div className="space-y-4">
            {templates.map(template => (
              <div
                key={template.id}
                className={`border rounded-lg p-4 cursor-pointer transition-colors ${
                  selectedTemplate?.id === template.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300'
                }`}
                onClick={() => selectTemplate(template)}
              >
                <div className="flex items-center justify-between mb-2">
                  <h3 className="font-medium">{template.name}</h3>
                  {template.featured && (
                    <span className="bg-yellow-100 text-yellow-800 text-xs px-2 py-1 rounded">
                      Featured
                    </span>
                  )}
                </div>
                <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                <div className="flex gap-2 text-xs text-gray-500">
                  <span className="bg-gray-100 px-2 py-1 rounded">{template.category}</span>
                  <span className="bg-gray-100 px-2 py-1 rounded">{template.difficulty}</span>
                  <span className="bg-gray-100 px-2 py-1 rounded">{template.estimatedTime}</span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Workflow Configuration */}
        <div>
          {selectedTemplate ? (
            <div>
              <h2 className="text-xl font-semibold mb-4">Configure Workflow</h2>

              {/* User API Key */}
              <div className="mb-4">
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  OpenAI API Key (Optional - BYOK)
                </label>
                <input
                  type="password"
                  value={userApiKey}
                  onChange={(e) => setUserApiKey(e.target.value)}
                  placeholder="sk-..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Leave empty to use system default
                </p>
              </div>

              {/* Input Fields */}
              <div className="space-y-4 mb-6">
                {Object.entries(inputs).map(([key, value]) => (
                  <div key={key}>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </label>
                    <textarea
                      value={value}
                      onChange={(e) => updateInput(key, e.target.value)}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                ))}
              </div>

              {/* Execute Button */}
              <button
                onClick={executeWorkflow}
                disabled={loading}
                className="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? 'Starting Workflow...' : 'Execute Workflow'}
              </button>

              {/* Execution Status */}
              {execution && (
                <div className="mt-6 p-4 border rounded-lg">
                  <h3 className="font-medium mb-2">Execution Status</h3>
                  <div className="space-y-2">
                    <div className="flex justify-between">
                      <span>Status:</span>
                      <span className={`font-medium ${
                        execution.status === 'completed' ? 'text-green-600' :
                        execution.status === 'failed' ? 'text-red-600' :
                        execution.status === 'running' ? 'text-blue-600' :
                        'text-gray-600'
                      }`}>
                        {execution.status}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Progress:</span>
                      <span>{execution.progress}%</span>
                    </div>
                    {execution.currentStep && (
                      <div className="flex justify-between">
                        <span>Current Step:</span>
                        <span>{execution.currentStep}</span>
                      </div>
                    )}
                    {execution.error && (
                      <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded">
                        <p className="text-sm text-red-600">{execution.error.message}</p>
                      </div>
                    )}
                  </div>

                  {/* Progress Bar */}
                  <div className="mt-4">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${execution.progress}%` }}
                      />
                    </div>
                  </div>

                  {/* View Results Button */}
                  {execution.status === 'completed' && (
                    <div className="mt-4">
                      <a
                        href={`/workflow/results/${execution.id}`}
                        className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      >
                        📄 View Results
                      </a>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-12">
              <p>Select a template to configure and execute a workflow</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
