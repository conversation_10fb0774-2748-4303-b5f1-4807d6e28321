/**
 * Root Layout
 * Main layout for the entire application
 */

import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'AuthenCIO CMS - Content Generation Platform',
  description: 'AI-powered content creation with human-in-the-loop review and workflow automation',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className="bg-gray-50 font-sans antialiased">
        {children}
      </body>
    </html>
  );
}
