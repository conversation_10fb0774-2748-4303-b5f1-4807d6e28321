/**
 * Dynamic Agent Consultation Service - Fresh Implementation
 * 
 * Main service for managing dynamic agent consultations within workflows
 */

import { v4 as uuidv4 } from 'uuid';
import { WorkflowAgentBridge } from './workflow-agent-bridge';
import {
  IConsultationService,
  AgentId,
  AgentConsultationRequest,
  AgentConsultationResponse,
  AgentMetrics,
  ConsultationContext,
  AgentError
} from '../agents/types';
import { AgentConsultationConfig, ConsultationTrigger } from './types';

export class DynamicAgentConsultationService implements IConsultationService {
  private metrics: AgentMetrics;
  private consultationHistory: Map<string, AgentConsultationResponse[]> = new Map();
  private stepConsultationCount: Map<string, number> = new Map(); // Track consultations per step

  constructor(private agentBridge: WorkflowAgentBridge) {
    this.initializeMetrics();
  }

  private initializeMetrics(): void {
    this.metrics = {
      totalConsultations: 0,
      successfulConsultations: 0,
      failedConsultations: 0,
      averageResponseTime: 0,
      averageConfidence: 0,
      successRate: 0,
      agentUtilization: {},
      lastUpdated: new Date().toISOString()
    };
  }

  async consultAgent(
    agentId: AgentId,
    workflowExecutionId: string,
    stepId: string,
    context: ConsultationContext
  ): Promise<AgentConsultationResponse> {
    const startTime = Date.now();

    try {
      console.log(`🤝 Starting consultation with ${agentId} for workflow ${workflowExecutionId}, step ${stepId}`);

      // Create consultation request
      const request = this.agentBridge.createConsultationRequest(
        agentId,
        workflowExecutionId,
        stepId,
        this.generateConsultationQuestion(agentId, context),
        context,
        'medium',
        30000
      );

      // Execute consultation
      const response = await this.agentBridge.consultAgent(agentId, request);

      // Update metrics
      this.updateMetricsOnSuccess(response);

      // Store in history
      this.storeConsultationInHistory(workflowExecutionId, response);

      console.log(`✅ Consultation completed with ${agentId}`);
      return response;

    } catch (error) {
      const processingTime = Date.now() - startTime;
      this.updateMetricsOnFailure(agentId, processingTime);

      console.error(`❌ Consultation failed with ${agentId}:`, error);
      throw error;
    }
  }

  async consultMultipleAgents(
    workflowExecutionId: string,
    stepId: string,
    context: ConsultationContext,
    config: AgentConsultationConfig
  ): Promise<AgentConsultationResponse[]> {
    console.log(`🔄 Starting multi-agent consultation for workflow ${workflowExecutionId}, step ${stepId}`);

    try {
      // Check consultation count to prevent infinite loops
      const stepKey = `${workflowExecutionId}-${stepId}`;
      const currentCount = this.stepConsultationCount.get(stepKey) || 0;

      if (currentCount >= config.maxConsultations) {
        console.log(`⚠️ Max consultations (${config.maxConsultations}) reached for step ${stepId}, skipping`);
        return [];
      }

      // Check if consultation should be triggered
      if (!await this.shouldTriggerConsultation(config, context)) {
        console.log(`ℹ️ Consultation not triggered for step ${stepId}`);
        return [];
      }

      // Select agents for consultation
      const selectedAgents = await this.selectAgentsForConsultation(config, context);
      
      if (selectedAgents.length === 0) {
        console.log(`ℹ️ No agents selected for consultation in step ${stepId}`);
        return [];
      }

      // Create consultation requests
      const requests = selectedAgents.map(agentId => 
        this.agentBridge.createConsultationRequest(
          agentId,
          workflowExecutionId,
          stepId,
          this.generateConsultationQuestion(agentId, context),
          context,
          this.determinePriority(config.triggers),
          config.timeoutMs
        )
      );

      // Execute consultations
      const responses = await this.agentBridge.consultMultipleAgents(requests);

      // Update consultation count
      this.stepConsultationCount.set(stepKey, currentCount + 1);

      // Handle fallback behavior if some consultations failed
      if (responses.length < requests.length && config.fallbackBehavior === 'fail') {
        throw new AgentError(
          `Only ${responses.length}/${requests.length} consultations succeeded`,
          'multiple' as AgentId,
          'PARTIAL_CONSULTATION_FAILURE'
        );
      }

      // Update metrics and store history
      responses.forEach(response => {
        this.updateMetricsOnSuccess(response);
        this.storeConsultationInHistory(workflowExecutionId, response);
      });

      console.log(`✅ Multi-agent consultation completed: ${responses.length}/${requests.length} successful (count: ${currentCount + 1}/${config.maxConsultations})`);
      return responses;

    } catch (error) {
      console.error(`❌ Multi-agent consultation failed:`, error);
      
      if (config.fallbackBehavior === 'continue') {
        return [];
      }
      
      throw error;
    }
  }

  async selectAgentsForContext(context: ConsultationContext): Promise<AgentId[]> {
    const selectedAgents: AgentId[] = [];

    // Content type based selection
    if (context.contentType === 'blog-post' || context.contentType === 'article') {
      selectedAgents.push('seo-keyword', 'content-strategy', 'market-research');
    }

    // Topic based selection
    if (context.topic) {
      const topicLower = context.topic.toLowerCase();
      
      if (topicLower.includes('market') || topicLower.includes('business') || topicLower.includes('industry')) {
        selectedAgents.push('market-research');
      }
      
      if (topicLower.includes('seo') || topicLower.includes('keyword') || topicLower.includes('search')) {
        selectedAgents.push('seo-keyword');
      }
    }

    // Feedback based selection
    if (context.feedback) {
      const feedbackLower = context.feedback.toLowerCase();
      
      if (feedbackLower.includes('seo') || feedbackLower.includes('keyword')) {
        selectedAgents.push('seo-keyword');
      }
      
      if (feedbackLower.includes('market') || feedbackLower.includes('audience')) {
        selectedAgents.push('market-research');
      }
      
      if (feedbackLower.includes('strategy') || feedbackLower.includes('structure')) {
        selectedAgents.push('content-strategy');
      }
    }

    // Complexity based selection
    const complexity = context.complexity || this.calculateContextComplexity(context);
    if (complexity > 0.6) {
      selectedAgents.push('content-strategy');
    }

    // Remove duplicates and ensure agents are available
    const uniqueAgents = [...new Set(selectedAgents)] as AgentId[];
    const availableAgents: AgentId[] = [];

    for (const agentId of uniqueAgents) {
      if (await this.agentBridge.isAgentAvailable(agentId)) {
        availableAgents.push(agentId);
      }
    }

    return availableAgents;
  }

  async shouldTriggerConsultation(config: AgentConsultationConfig, context: ConsultationContext): Promise<boolean> {
    if (!config.enabled) {
      return false;
    }

    for (const trigger of config.triggers) {
      if (await this.evaluateTrigger(trigger, context)) {
        return true;
      }
    }

    return false;
  }

  private async evaluateTrigger(trigger: ConsultationTrigger, context: ConsultationContext): Promise<boolean> {
    switch (trigger.type) {
      case 'always':
        return true;

      case 'quality_threshold':
        if (trigger.condition?.threshold && context.qualityScore !== undefined) {
          return context.qualityScore < trigger.condition.threshold;
        }
        return false;

      case 'feedback_keywords':
        if (trigger.condition?.keywords && context.feedback) {
          const keywords = trigger.condition.keywords;
          const feedbackLower = context.feedback.toLowerCase();
          return keywords.some(keyword => feedbackLower.includes(keyword.toLowerCase()));
        }
        return false;

      case 'content_complexity':
        if (trigger.condition?.threshold) {
          const complexity = context.complexity || this.calculateContextComplexity(context);
          return complexity > trigger.condition.threshold;
        }
        return false;

      default:
        return false;
    }
  }

  private async selectAgentsForConsultation(
    config: AgentConsultationConfig,
    context: ConsultationContext
  ): Promise<AgentId[]> {
    const selectedAgents: Set<AgentId> = new Set();

    // Collect agents from triggered conditions
    for (const trigger of config.triggers) {
      if (await this.evaluateTrigger(trigger, context)) {
        trigger.agents.forEach(agentId => selectedAgents.add(agentId));
      }
    }

    // Add context-based agents
    const contextAgents = await this.selectAgentsForContext(context);
    contextAgents.forEach(agentId => selectedAgents.add(agentId));

    // Limit to max consultations
    const agentArray = Array.from(selectedAgents);
    return agentArray.slice(0, config.maxConsultations);
  }

  private generateConsultationQuestion(agentId: AgentId, context: ConsultationContext): string {
    const topic = context.topic || 'the given topic';
    
    switch (agentId) {
      case 'seo-keyword':
        return `Provide keyword research and SEO optimization recommendations for "${topic}" targeting ${context.targetAudience || 'general audience'}`;
      
      case 'market-research':
        return `Conduct market analysis for "${topic}" including competitor research and audience insights for ${context.targetAudience || 'general audience'}`;
      
      case 'content-strategy':
        return `Develop content strategy and structure recommendations for "${topic}" as ${context.contentType || 'content'} targeting ${context.targetAudience || 'general audience'}`;
      
      default:
        return `Provide expert consultation for "${topic}"`;
    }
  }

  private determinePriority(triggers: ConsultationTrigger[]): 'low' | 'medium' | 'high' {
    const priorities = triggers.map(t => t.priority);
    
    if (priorities.includes('high')) return 'high';
    if (priorities.includes('medium')) return 'medium';
    return 'low';
  }

  private calculateContextComplexity(context: ConsultationContext): number {
    let complexity = 0;

    // Content length factor
    if (context.content) {
      const contentLength = context.content.length;
      if (contentLength > 2000) complexity += 0.3;
      else if (contentLength > 1000) complexity += 0.2;
      else if (contentLength > 500) complexity += 0.1;
    }

    // Topic complexity factor
    if (context.topic) {
      const technicalKeywords = ['algorithm', 'api', 'framework', 'architecture', 'implementation'];
      const hasTechnicalTerms = technicalKeywords.some(keyword => 
        context.topic!.toLowerCase().includes(keyword)
      );
      if (hasTechnicalTerms) complexity += 0.2;
    }

    // Target audience factor
    if (context.targetAudience) {
      const expertAudiences = ['technical experts', 'developers', 'engineers', 'professionals'];
      const isExpertAudience = expertAudiences.some(audience => 
        context.targetAudience!.toLowerCase().includes(audience)
      );
      if (isExpertAudience) complexity += 0.2;
    }

    // Multiple goals factor
    if (context.goals && Array.isArray(context.goals) && context.goals.length > 3) {
      complexity += 0.1;
    }

    return Math.min(complexity, 1.0);
  }

  private updateMetricsOnSuccess(response: AgentConsultationResponse): void {
    this.metrics.totalConsultations++;
    this.metrics.successfulConsultations++;
    
    // Update agent utilization
    this.metrics.agentUtilization[response.agentId] = 
      (this.metrics.agentUtilization[response.agentId] || 0) + 1;

    // Update averages
    this.updateAverages(response.processingTime, response.confidence);
  }

  private updateMetricsOnFailure(agentId: AgentId, processingTime: number): void {
    this.metrics.totalConsultations++;
    this.metrics.failedConsultations++;
    
    // Update agent utilization (failed attempts still count)
    this.metrics.agentUtilization[agentId] = 
      (this.metrics.agentUtilization[agentId] || 0) + 1;

    // Update averages
    this.updateAverages(processingTime, 0);
  }

  private updateAverages(processingTime: number, confidence: number): void {
    const total = this.metrics.totalConsultations;
    
    // Update average response time
    this.metrics.averageResponseTime = 
      ((this.metrics.averageResponseTime * (total - 1)) + processingTime) / total;

    // Update average confidence (only for successful consultations)
    if (confidence > 0) {
      const successful = this.metrics.successfulConsultations;
      this.metrics.averageConfidence = 
        ((this.metrics.averageConfidence * (successful - 1)) + confidence) / successful;
    }

    // Update success rate
    this.metrics.successRate = this.metrics.successfulConsultations / this.metrics.totalConsultations;
    
    // Update timestamp
    this.metrics.lastUpdated = new Date().toISOString();
  }

  private storeConsultationInHistory(workflowExecutionId: string, response: AgentConsultationResponse): void {
    if (!this.consultationHistory.has(workflowExecutionId)) {
      this.consultationHistory.set(workflowExecutionId, []);
    }
    
    this.consultationHistory.get(workflowExecutionId)!.push(response);
  }

  getMetrics(): AgentMetrics {
    return { ...this.metrics };
  }

  clearMetrics(): void {
    this.initializeMetrics();
    this.consultationHistory.clear();
    this.stepConsultationCount.clear();
  }

  /**
   * Get consultation history for a specific workflow execution
   */
  getConsultationHistory(workflowExecutionId: string): AgentConsultationResponse[] {
    return this.consultationHistory.get(workflowExecutionId) || [];
  }

  /**
   * Get all consultation history
   */
  getAllConsultationHistory(): Record<string, AgentConsultationResponse[]> {
    return Object.fromEntries(this.consultationHistory.entries());
  }
}
