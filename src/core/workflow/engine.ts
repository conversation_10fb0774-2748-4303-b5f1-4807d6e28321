/**
 * Simplified Workflow Engine
 * Basic workflow execution without complex orchestration
 * Fixed execution scope issue
 */

import { v4 as uuidv4 } from 'uuid';
import {
  IWorkflowEngine,
  Workflow,
  WorkflowExecution,
  WorkflowStep,
  StepResult,
  StepType,
  ExecutionStatus,
  StepStatus,
  WorkflowFilters,
  ReviewDecision,
  VariableContext,
  ExecutionMetadata,
  WorkflowArtifact,
  ArtifactType,
  ArtifactStatus,
  ApprovalDecision,
  ApprovalStatus,
  StepCondition,
  ConditionalRule,
  ApprovalGate
} from './types';
import { ISimplifiedStateStore, ContentItem, ContentType, ContentStatus } from '../state/types';
import { SimplifiedStateStore } from '../state/store';
import { AIModelManager } from '../ai/model-manager';
import { SimplifiedReviewSystem } from '../review/system';
import { ReviewManager } from '../review/manager';
import { userManager } from '../../utils/user-manager';
import { errorHandler, ErrorType, ErrorSeverity } from '../utils/error-handler';
import {
  workflowArtifactToContentItem,
  contentItemToWorkflowArtifact,
  normalizeStepResult,
  normalizeExecutionMetadata,
  validateTypeConversion,
  isWorkflowArtifact,
  isContentItem
} from '../utils/type-converters';
import { EnhancedAIGenerationStep } from './enhanced-ai-generation-step';

export class WorkflowEngine implements IWorkflowEngine {
  private reviewManager: ReviewManager;
  private enhancedAIStep: EnhancedAIGenerationStep;

  constructor(
    private stateStore: ISimplifiedStateStore,
    private aiManager: AIModelManager,
    private reviewSystem: SimplifiedReviewSystem
  ) {
    this.reviewManager = new ReviewManager();
    this.enhancedAIStep = new EnhancedAIGenerationStep();
  }

  // Workflow management
  async createWorkflow(workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    try {
      // Validate workflow data
      this.validateWorkflowData(workflow);

      const id = uuidv4();
      const now = new Date().toISOString();

      const newWorkflow: Workflow = {
        ...workflow,
        id,
        createdAt: now,
        updatedAt: now
      };

      console.log(`🔄 Creating workflow: ${id} (${workflow.name})`);
      await this.stateStore.setWorkflow(newWorkflow);

      // Verify the workflow was created with retry logic for concurrent operations
      let verifyWorkflow = null;
      let retries = 3;

      while (retries > 0 && !verifyWorkflow) {
        await new Promise(resolve => setTimeout(resolve, 10)); // Small delay
        verifyWorkflow = await this.stateStore.getWorkflow(id);
        retries--;
      }

      if (!verifyWorkflow) {
        throw new Error(`Failed to verify workflow creation after retries: ${id}`);
      }

      console.log(`✅ Workflow created and verified: ${id} (${workflow.name})`);
      return id;
    } catch (error) {
      console.error(`❌ Failed to create workflow: ${workflow.name}`, error);

      // If it's a validation error, throw it directly without wrapping
      if (error instanceof Error && (
        error.message.includes('required') ||
        error.message.includes('must be') ||
        error.message.includes('cannot be empty')
      )) {
        throw error;
      }

      const standardError = errorHandler.handleError(error, {
        operation: 'createWorkflow',
        workflowName: workflow.name,
        workflowSteps: workflow.steps?.length || 0
      });
      throw standardError;
    }
  }

  private validateWorkflowData(workflow: Omit<Workflow, 'id' | 'createdAt' | 'updatedAt'>): void {
    if (!workflow.name || workflow.name.trim() === '') {
      throw new Error('Workflow name is required and cannot be empty');
    }

    if (!workflow.description || workflow.description.trim() === '') {
      throw new Error('Workflow description is required and cannot be empty');
    }

    if (!workflow.steps || !Array.isArray(workflow.steps)) {
      throw new Error('Workflow steps must be an array');
    }

    if (!workflow.metadata) {
      throw new Error('Workflow metadata is required');
    }

    // Validate metadata fields
    if (!workflow.metadata.category || workflow.metadata.category.trim() === '') {
      throw new Error('Workflow category is required');
    }

    if (!workflow.metadata.difficulty || !['easy', 'medium', 'hard'].includes(workflow.metadata.difficulty)) {
      throw new Error('Workflow difficulty must be easy, medium, or hard');
    }

    if (typeof workflow.metadata.estimatedTime !== 'number' || workflow.metadata.estimatedTime <= 0) {
      throw new Error('Workflow estimated time must be a positive number');
    }
  }

  async getWorkflow(id: string): Promise<Workflow | null> {
    try {
      return await this.stateStore.getWorkflow(id);
    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'getWorkflow',
        workflowId: id
      });
      throw standardError;
    }
  }

  async updateWorkflow(id: string, updates: Partial<Workflow>): Promise<void> {
    const workflow = await this.getWorkflow(id);
    if (!workflow) {
      throw new Error(`Workflow ${id} not found`);
    }

    const updatedWorkflow: Workflow = {
      ...workflow,
      ...updates,
      updatedAt: new Date().toISOString()
    };

    await this.stateStore.setWorkflow(updatedWorkflow);
  }

  async deleteWorkflow(id: string): Promise<void> {
    // In a full implementation, this would remove the workflow
    // For now, just mark as deleted or implement soft delete
    throw new Error('Delete workflow not implemented yet');
  }

  async listWorkflows(filters?: WorkflowFilters): Promise<Workflow[]> {
    const workflows = await this.stateStore.getAllWorkflows();

    if (!filters) {
      return workflows;
    }

    return workflows.filter(workflow => {
      if (filters.category && workflow.metadata.category !== filters.category) {
        return false;
      }
      if (filters.difficulty && workflow.metadata.difficulty !== filters.difficulty) {
        return false;
      }
      if (filters.featured !== undefined && workflow.metadata.featured !== filters.featured) {
        return false;
      }
      if (filters.tags && !filters.tags.some(tag => workflow.metadata.tags.includes(tag))) {
        return false;
      }
      return true;
    });
  }

  // Execution management
  async executeWorkflow(
    workflowId: string,
    inputs: Record<string, any>,
    metadata?: Partial<ExecutionMetadata>
  ): Promise<string> {
    try {
      const workflow = await this.getWorkflow(workflowId);
      if (!workflow) {
        const error = errorHandler.createError(
          ErrorType.WORKFLOW,
          'WORKFLOW_NOT_FOUND',
          `Workflow ${workflowId} not found`,
          { workflowId }
        );
        throw error;
      }

      const executionId = uuidv4();
      const now = new Date().toISOString();

      const execution: WorkflowExecution = {
        id: executionId,
        workflowId,
        status: ExecutionStatus.RUNNING,
        inputs,
        outputs: {},
        stepResults: {},
        progress: 0,
        startedAt: now,
        metadata: normalizeExecutionMetadata(metadata || {})
      };

      await this.stateStore.setExecution(execution);

      console.log(`🚀 Workflow execution started: ${executionId} (${workflow.name})`);

      // Start execution asynchronously
      this.executeWorkflowSteps(executionId).catch(error => {
        const standardError = errorHandler.handleError(error, {
          operation: 'executeWorkflowSteps',
          executionId,
          workflowId
        });
        console.error(`❌ Workflow execution ${executionId} failed:`, standardError.message);
        this.markExecutionFailed(executionId, standardError);
      });

      return executionId;
    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'executeWorkflow',
        workflowId,
        inputs
      });
      throw standardError;
    }
  }

  async getExecution(id: string): Promise<WorkflowExecution | null> {
    return await this.stateStore.getExecution(id);
  }

  async pauseExecution(id: string): Promise<void> {
    const execution = await this.getExecution(id);
    if (!execution) {
      throw new Error(`Execution ${id} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.PAUSED
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  async resumeExecution(id: string): Promise<void> {
    const execution = await this.getExecution(id);
    if (!execution) {
      throw new Error(`Execution ${id} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.RUNNING
    };

    await this.stateStore.setExecution(updatedExecution);

    // Resume execution
    this.executeWorkflowSteps(id).catch(error => {
      console.error(`Workflow execution ${id} failed on resume:`, error);
      this.markExecutionFailed(id, error);
    });
  }

  async cancelExecution(id: string): Promise<void> {
    const execution = await this.getExecution(id);
    if (!execution) {
      throw new Error(`Execution ${id} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.CANCELLED,
      completedAt: new Date().toISOString()
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  // Step execution
  async executeStep(executionId: string, stepId: string): Promise<StepResult> {
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const workflow = await this.getWorkflow(execution.workflowId);
    if (!workflow) {
      throw new Error(`Workflow ${execution.workflowId} not found`);
    }

    const step = workflow.steps.find(s => s.id === stepId);
    if (!step) {
      throw new Error(`Step ${stepId} not found in workflow`);
    }

    return await this.executeWorkflowStep(execution, step);
  }

  async retryStep(executionId: string, stepId: string): Promise<StepResult> {
    // Reset step status and re-execute
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    // Remove previous step result
    const updatedExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: undefined
      }
    };
    delete updatedExecution.stepResults[stepId];

    await this.stateStore.setExecution(updatedExecution);

    return await this.executeStep(executionId, stepId);
  }

  // Review handling
  async submitReview(executionId: string, stepId: string, decision: ReviewDecision): Promise<void> {
    // This would be called by the review system
    // For now, just update the step status
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const stepResult = execution.stepResults[stepId];
    if (!stepResult) {
      throw new Error(`Step result ${stepId} not found`);
    }

    const updatedStepResult: StepResult = {
      ...stepResult,
      status: decision.approved ? StepStatus.COMPLETED : StepStatus.FAILED,
      completedAt: new Date().toISOString(),
      outputs: {
        ...stepResult.outputs,
        review_decision: decision.approved ? 'approved' : 'rejected',
        review_feedback: decision.feedback
      }
    };

    const updatedExecution: WorkflowExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: updatedStepResult
      }
    };

    await this.stateStore.setExecution(updatedExecution);

    // Continue execution if approved
    if (decision.approved) {
      this.executeWorkflowSteps(executionId).catch(error => {
        console.error(`Workflow execution ${executionId} failed after review:`, error);
        this.markExecutionFailed(executionId, error);
      });
    }
  }

  // Artifact management
  async createArtifact(executionId: string, stepId: string, artifact: Omit<WorkflowArtifact, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> {
    const artifactId = uuidv4();
    const now = new Date().toISOString();

    const newArtifact: WorkflowArtifact = {
      ...artifact,
      id: artifactId,
      createdAt: now,
      updatedAt: now
    };

    // Store artifact as content item
    const contentItem: ContentItem = {
      id: artifactId,
      type: this.mapArtifactTypeToContentType(artifact.type),
      title: artifact.title,
      content: artifact.content,
      status: artifact.status === ArtifactStatus.APPROVED ? ContentStatus.APPROVED : ContentStatus.PENDING,
      executionId,
      stepId,
      createdAt: now,
      updatedAt: now,
      metadata: {
        stepId,
        executionId,
        version: artifact.version,
        createdBy: artifact.createdBy,
        artifactType: artifact.type
      }
    };

    await this.stateStore.setContent(contentItem);

    // Update step result with artifact ID
    const execution = await this.getExecution(executionId);
    if (execution && execution.stepResults[stepId]) {
      execution.stepResults[stepId].artifactId = artifactId;
      await this.stateStore.setExecution(execution);
    }

    return artifactId;
  }

  async getArtifact(artifactId: string): Promise<WorkflowArtifact | null> {
    const content = await this.stateStore.getContent(artifactId);
    if (!content || !content.metadata) {
      return null;
    }

    return {
      id: content.id,
      stepId: content.metadata.stepId || content.stepId,
      executionId: content.metadata.executionId || content.executionId,
      type: content.metadata.artifactType as ArtifactType || ArtifactType.CONTENT_DRAFT,
      title: content.title,
      content: content.content,
      status: content.status === ContentStatus.APPROVED ? ArtifactStatus.APPROVED : ArtifactStatus.PENDING_APPROVAL,
      version: content.metadata.version || 1,
      createdAt: content.createdAt,
      updatedAt: content.updatedAt,
      createdBy: content.metadata.createdBy || 'system',
      approvedBy: content.metadata.approvedBy,
      approvedAt: content.metadata.approvedAt,
      rejectedBy: content.metadata.rejectedBy,
      rejectedAt: content.metadata.rejectedAt,
      rejectionReason: content.metadata.rejectionReason
    };
  }

  async updateArtifact(artifactId: string, updates: Partial<WorkflowArtifact>): Promise<void> {
    const content = await this.stateStore.getContent(artifactId);
    if (!content) {
      throw new Error(`Artifact ${artifactId} not found`);
    }

    const updatedContent: ContentItem = {
      ...content,
      title: updates.title || content.title,
      content: updates.content || content.content,
      status: updates.status === ArtifactStatus.APPROVED ? ContentStatus.APPROVED : content.status,
      updatedAt: new Date().toISOString(),
      metadata: {
        ...content.metadata,
        version: updates.version || content.metadata?.version || 1,
        approvedBy: updates.approvedBy,
        approvedAt: updates.approvedAt,
        rejectedBy: updates.rejectedBy,
        rejectedAt: updates.rejectedAt,
        rejectionReason: updates.rejectionReason
      }
    };

    await this.stateStore.setContent(updatedContent);
  }

  // Store access
  getStore(): ISimplifiedStateStore {
    return this.stateStore;
  }

  // Approval handling
  async submitApproval(artifactId: string, decision: ApprovalDecision): Promise<void> {
    const artifact = await this.getArtifact(artifactId);
    if (!artifact) {
      throw new Error(`Artifact ${artifactId} not found`);
    }

    // Update artifact status based on decision
    const updates: Partial<WorkflowArtifact> = {
      status: decision.approved ? ArtifactStatus.APPROVED : ArtifactStatus.REJECTED,
      updatedAt: decision.timestamp
    };

    if (decision.approved) {
      updates.approvedBy = decision.approver;
      updates.approvedAt = decision.timestamp;
    } else {
      updates.rejectedBy = decision.approver;
      updates.rejectedAt = decision.timestamp;
      updates.rejectionReason = decision.reason;
    }

    await this.updateArtifact(artifactId, updates);

    // If approved, continue workflow execution
    if (decision.approved) {
      console.log(`✅ Artifact ${artifactId} approved - resuming workflow execution ${artifact.executionId}`);

      // Update step status to approved
      const execution = await this.getExecution(artifact.executionId);
      if (execution && execution.stepResults[artifact.stepId]) {
        console.log(`📝 Updating step ${artifact.stepId} status to APPROVED`);
        execution.stepResults[artifact.stepId].status = StepStatus.APPROVED;
        execution.stepResults[artifact.stepId].approvedBy = decision.approver;
        execution.stepResults[artifact.stepId].approvedAt = decision.timestamp;
        execution.stepResults[artifact.stepId].completedAt = decision.timestamp;

        // Resume execution status to RUNNING
        execution.status = ExecutionStatus.RUNNING;
        await this.stateStore.setExecution(execution);
        console.log(`💾 Execution ${artifact.executionId} updated with approved step and status set to RUNNING`);

        // Resume workflow execution
        console.log(`🚀 Resuming workflow execution ${artifact.executionId}...`);
        this.executeWorkflowSteps(artifact.executionId).catch(error => {
          console.error(`Workflow execution ${artifact.executionId} failed after approval:`, error);
          this.markExecutionFailed(artifact.executionId, error);
        });
      } else {
        console.log(`⚠️ Could not find execution ${artifact.executionId} or step ${artifact.stepId}`);
      }
    } else {
      // Handle rejection with feedback processing
      await this.handleRejectionWithFeedback(artifact, decision);
    }
  }

  /**
   * Handle artifact rejection with feedback and regeneration
   */
  async handleRejectionWithFeedback(artifact: WorkflowArtifact, decision: ApprovalDecision): Promise<void> {
    console.log(`❌ Artifact ${artifact.id} rejected with feedback: ${decision.reason}`);

    // Check if feedback is provided and actionable
    if (!decision.reason || decision.reason.trim().length < 10) {
      console.log(`⚠️ No actionable feedback provided, marking step as rejected without regeneration`);
      await this.markStepRejected(artifact, decision);
      return;
    }

    try {
      console.log(`🔄 Starting regeneration for artifact ${artifact.id} based on feedback`);

      // Regenerate artifact content using AI
      const regeneratedContent = await this.regenerateArtifactContent(artifact, decision.reason);

      // Create new artifact with regenerated content
      const newArtifactId = await this.createArtifact(artifact.executionId, artifact.stepId, {
        stepId: artifact.stepId,
        executionId: artifact.executionId,
        type: artifact.type,
        title: `${artifact.title} (Improved v${(artifact.version || 1) + 1})`,
        content: regeneratedContent,
        status: ArtifactStatus.PENDING_APPROVAL,
        version: (artifact.version || 1) + 1,
        createdBy: 'regeneration-system'
      });

      console.log(`✅ Regeneration successful: ${artifact.id} -> ${newArtifactId}`);

      // Update the step to point to the new artifact
      await this.updateStepWithRegeneratedArtifact(artifact, newArtifactId);

      // The new artifact will need approval again, so workflow stays paused
      console.log(`⏸️ Workflow paused for approval of regenerated artifact ${newArtifactId}`);

    } catch (error) {
      console.error(`❌ Error during regeneration process:`, error);
      await this.markStepRejected(artifact, decision);
    }
  }

  /**
   * Regenerate artifact content using AI based on feedback
   */
  private async regenerateArtifactContent(artifact: WorkflowArtifact, feedback: string): Promise<any> {
    console.log(`🤖 Regenerating content for artifact ${artifact.id} with feedback: ${feedback}`);

    // Build improvement prompt
    const systemPrompt = `You are an expert content improvement AI. Your task is to regenerate and improve content based on human feedback.

Guidelines:
1. Address all feedback points specifically and thoroughly
2. Maintain the original intent and core message
3. Improve quality while preserving the content's purpose
4. Ensure the output is better than the original in the areas mentioned
5. Keep the same format and structure unless feedback suggests otherwise

Focus on making targeted improvements rather than complete rewrites unless explicitly requested.`;

    const userPrompt = `Please improve the following content based on the feedback provided:

ORIGINAL CONTENT:
${JSON.stringify(artifact.content, null, 2)}

FEEDBACK:
${feedback}

Please provide the improved content in the same format as the original, focusing on the specific feedback provided.`;

    try {
      // Generate improved content using AI
      const result = await this.aiManager.generate(userPrompt, {
        provider: 'openai',
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 2000,
        systemPrompt
      });

      // Try to parse as JSON if original was JSON
      let improvedContent = result.content;
      if (typeof artifact.content === 'object') {
        try {
          improvedContent = JSON.parse(result.content);
        } catch {
          // If parsing fails, wrap in appropriate structure
          improvedContent = {
            content: result.content,
            improved: true,
            feedback_addressed: feedback
          };
        }
      }

      console.log(`✅ Content regenerated successfully for artifact ${artifact.id}`);
      return improvedContent;

    } catch (error) {
      console.error(`❌ AI regeneration failed:`, error);
      throw new Error(`Failed to regenerate content: ${error.message}`);
    }
  }

  /**
   * Mark step as rejected without regeneration
   */
  private async markStepRejected(artifact: WorkflowArtifact, decision: ApprovalDecision): Promise<void> {
    const execution = await this.getExecution(artifact.executionId);
    if (execution && execution.stepResults[artifact.stepId]) {
      execution.stepResults[artifact.stepId].status = StepStatus.REJECTED;
      execution.stepResults[artifact.stepId].rejectionReason = decision.reason;
      execution.stepResults[artifact.stepId].error = `Artifact rejected: ${decision.reason}`;

      await this.stateStore.setExecution(execution);
      console.log(`📝 Step ${artifact.stepId} marked as rejected`);
    }
  }

  /**
   * Update step to point to regenerated artifact
   */
  private async updateStepWithRegeneratedArtifact(originalArtifact: WorkflowArtifact, newArtifactId: string): Promise<void> {
    const execution = await this.getExecution(originalArtifact.executionId);
    if (execution && execution.stepResults[originalArtifact.stepId]) {
      // Update step result with new artifact ID
      execution.stepResults[originalArtifact.stepId].artifactId = newArtifactId;
      execution.stepResults[originalArtifact.stepId].status = StepStatus.WAITING_APPROVAL;
      execution.stepResults[originalArtifact.stepId].outputs = {
        ...execution.stepResults[originalArtifact.stepId].outputs,
        artifact_id: newArtifactId,
        regenerated_from: originalArtifact.id,
        regeneration_timestamp: new Date().toISOString()
      };

      await this.stateStore.setExecution(execution);
      console.log(`📝 Step ${originalArtifact.stepId} updated with regenerated artifact ${newArtifactId}`);
    }
  }

  async getApprovalStatus(artifactId: string): Promise<ApprovalStatus> {
    const artifact = await this.getArtifact(artifactId);
    if (!artifact) {
      throw new Error(`Artifact ${artifactId} not found`);
    }

    // For now, return a simple approval status
    // In a full implementation, this would track multiple approvers
    return {
      artifactId,
      status: artifact.status,
      approvals: artifact.approvedBy ? [{
        approved: true,
        approver: artifact.approvedBy,
        timestamp: artifact.approvedAt || new Date().toISOString(),
        feedback: ''
      }] : [],
      requiredApprovals: 1,
      pendingApprovers: artifact.status === ArtifactStatus.PENDING_APPROVAL ? userManager.getDefaultApprovers() : [],
      canProceed: artifact.status === ArtifactStatus.APPROVED,
      escalated: false,
      escalationLevel: 0
    };
  }

  // Private methods for workflow execution
  private async executeWorkflowSteps(executionId: string): Promise<void> {
    console.log(`🚀 Starting executeWorkflowSteps for execution ${executionId}`);

    const execution = await this.getExecution(executionId);
    if (!execution) {
      console.log(`❌ Execution ${executionId} not found`);
      return;
    }

    console.log(`📋 Found execution ${executionId}, status: ${execution.status}, progress: ${execution.progress}%`);

    if (execution.status !== ExecutionStatus.RUNNING) {
      console.log(`⏸️ Execution ${executionId} is not running (status: ${execution.status}), skipping execution`);
      return;
    }

    const workflow = await this.getWorkflow(execution.workflowId);
    if (!workflow) {
      console.log(`❌ Workflow ${execution.workflowId} not found`);
      throw new Error(`Workflow ${execution.workflowId} not found`);
    }

    console.log(`📋 Found workflow ${execution.workflowId}: ${workflow.name}`);
    console.log(`📋 Current step: ${execution.currentStep || 'none'}`);
    console.log(`📋 Step results:`, Object.keys(execution.stepResults).map(stepId => ({
      stepId,
      status: execution.stepResults[stepId].status
    })));

    // Find next step to execute
    console.log(`🔍 Looking for next step to execute...`);
    const nextStep = await this.findNextStep(workflow, execution);
    if (!nextStep) {
      // Check if we're waiting for any approvals
      const waitingSteps = Object.values(execution.stepResults).filter(result =>
        result.status === StepStatus.WAITING_APPROVAL || result.status === StepStatus.WAITING_REVIEW
      );

      if (waitingSteps.length > 0) {
        // Workflow is paused waiting for approval
        execution.status = ExecutionStatus.PAUSED;
        await this.stateStore.setExecution(execution);
        console.log(`⏸️ Workflow ${execution.id} paused - waiting for ${waitingSteps.length} approval(s)`);
        return;
      }

      // All steps completed
      await this.markExecutionCompleted(executionId);
      return;
    }

    // Check if step is waiting for review or approval
    const stepResult = execution.stepResults[nextStep.id];
    if (stepResult && (
      stepResult.status === StepStatus.WAITING_REVIEW ||
      stepResult.status === StepStatus.WAITING_APPROVAL
    )) {
      return; // Wait for review or approval
    }

    // Execute the step
    try {
      const stepResult = await this.executeWorkflowStep(execution, nextStep);

      // Check if workflow should pause after this step
      if (stepResult.status === StepStatus.WAITING_APPROVAL ||
          stepResult.status === StepStatus.WAITING_REVIEW) {
        console.log(`⏸️ Workflow ${executionId} paused after step ${nextStep.id} - status: ${stepResult.status}`);

        // Update execution status to paused
        const updatedExecution = await this.getExecution(executionId);
        if (updatedExecution) {
          updatedExecution.status = ExecutionStatus.PAUSED;
          await this.stateStore.setExecution(updatedExecution);
        }
        return; // Stop execution here
      }

      // Continue with next step only if not waiting for approval/review
      setTimeout(() => {
        this.executeWorkflowSteps(executionId).catch(error => {
          console.error(`Workflow execution ${executionId} failed:`, error);
          this.markExecutionFailed(executionId, error);
        });
      }, 100); // Small delay to prevent stack overflow

    } catch (error) {
      console.error(`Step ${nextStep.id} failed:`, error);
      await this.markStepFailed(executionId, nextStep.id, error);
      throw error;
    }
  }

  private async findNextStep(workflow: Workflow, execution: WorkflowExecution): Promise<WorkflowStep | null> {
    console.log(`🔍 Finding next step for execution ${execution.id}`);
    console.log(`📋 Available steps: ${workflow.steps.map(s => s.id).join(', ')}`);

    for (const step of workflow.steps) {
      const stepResult = execution.stepResults[step.id];

      console.log(`🔍 Checking step ${step.id}:`, {
        hasResult: !!stepResult,
        status: stepResult?.status,
        dependencies: step.dependencies
      });

      // Skip steps that don't need execution
      if (stepResult) {
        if (stepResult.status === StepStatus.COMPLETED ||
            stepResult.status === StepStatus.APPROVED ||
            stepResult.status === StepStatus.WAITING_REVIEW ||
            stepResult.status === StepStatus.WAITING_APPROVAL ||
            stepResult.status === StepStatus.FAILED) {
          console.log(`⏭️ Skipping step ${step.id} - status: ${stepResult.status}`);
          continue;
        }
      }

      // Check dependencies - steps must be completed or approved
      const dependenciesMet = await this.checkDependenciesMet(step.dependencies, execution);

      console.log(`🔍 Checking dependencies for step ${step.id}:`, {
        dependencies: step.dependencies,
        dependenciesMet,
        stepType: step.type
      });

      if (dependenciesMet) {
        console.log(`✅ Dependencies met for step ${step.id} - this is the next step!`);

        // Check step condition if it exists
        if (step.condition && !this.evaluateStepCondition(step.condition, execution)) {
          // Mark step as skipped
          this.markStepSkipped(execution.id, step.id, 'condition_not_met');
          continue;
        }

        return step;
      } else {
        console.log(`❌ Dependencies NOT met for step ${step.id}`);
      }
    }

    console.log(`❌ No next step found for execution ${execution.id}`);
    return null;
  }

  /**
   * Check if step dependencies are met, including approval gate requirements
   */
  private async checkDependenciesMet(dependencies: string[], execution: WorkflowExecution): Promise<boolean> {
    for (const depId of dependencies) {
      const depResult = execution.stepResults[depId];

      if (!depResult) {
        console.log(`❌ Dependency ${depId} not found`);
        return false;
      }

      // For approval gates, check if artifact is actually approved
      if (depResult.stepType === 'approval_gate' || depResult.approvalRequired) {
        if (depResult.artifactId) {
          try {
            const artifact = await this.getArtifact(depResult.artifactId);
            if (!artifact || artifact.status !== ArtifactStatus.APPROVED) {
              console.log(`⏸️ Approval gate ${depId} not approved yet (artifact: ${depResult.artifactId}, status: ${artifact?.status})`);
              return false;
            }
            console.log(`✅ Approval gate ${depId} is approved`);
          } catch (error) {
            console.log(`❌ Error checking artifact for ${depId}:`, error);
            return false;
          }
        } else {
          console.log(`❌ Approval gate ${depId} missing artifact ID`);
          return false;
        }
      } else {
        // Regular steps just need to be completed
        if (depResult.status !== StepStatus.COMPLETED && depResult.status !== StepStatus.APPROVED) {
          console.log(`❌ Regular step ${depId} not completed (status: ${depResult.status})`);
          return false;
        }
        console.log(`✅ Regular step ${depId} is completed`);
      }
    }

    return true;
  }

  // Evaluate step condition
  private evaluateStepCondition(condition: StepCondition, execution: WorkflowExecution): boolean {
    const results = condition.rules.map(rule => this.evaluateConditionalRule(rule, execution));

    if (condition.logic === 'AND') {
      return results.every(result => result);
    } else {
      return results.some(result => result);
    }
  }

  // Evaluate a single conditional rule
  private evaluateConditionalRule(rule: ConditionalRule, execution: WorkflowExecution): boolean {
    // Get variable value from execution context
    const variableValue = this.getVariableValue(rule.variable, execution);

    switch (rule.operator) {
      case 'equals':
        return variableValue === rule.value;
      case 'not_equals':
        return variableValue !== rule.value;
      case 'contains':
        return String(variableValue).includes(String(rule.value));
      case 'not_contains':
        return !String(variableValue).includes(String(rule.value));
      case 'greater_than':
        return Number(variableValue) > Number(rule.value);
      case 'less_than':
        return Number(variableValue) < Number(rule.value);
      case 'exists':
        return variableValue !== undefined && variableValue !== null;
      case 'not_exists':
        return variableValue === undefined || variableValue === null;
      default:
        return false;
    }
  }

  // Get variable value from execution context
  private getVariableValue(variableName: string, execution: WorkflowExecution): any {
    // Check inputs first
    if (execution.inputs[variableName] !== undefined) {
      return execution.inputs[variableName];
    }

    // Check step outputs
    for (const stepResult of Object.values(execution.stepResults)) {
      if (stepResult.outputs[variableName] !== undefined) {
        return stepResult.outputs[variableName];
      }
    }

    // Check execution outputs
    if (execution.outputs[variableName] !== undefined) {
      return execution.outputs[variableName];
    }

    return undefined;
  }

  // Mark step as skipped
  private async markStepSkipped(executionId: string, stepId: string, reason: string): Promise<void> {
    const stepResult: StepResult = {
      stepId,
      status: StepStatus.SKIPPED,
      inputs: {},
      outputs: {},
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      duration: 0,
      metadata: { reason }
    };

    await this.updateStepResult(executionId, stepId, stepResult);
  }

  private async executeWorkflowStep(execution: WorkflowExecution, step: WorkflowStep): Promise<StepResult> {
    const startTime = new Date().toISOString();

    // Create initial step result
    const stepResult: StepResult = {
      stepId: step.id,
      status: StepStatus.RUNNING,
      inputs: this.buildStepInputs(execution, step),
      outputs: {},
      startedAt: startTime,
      stepType: step.type,
      approvalRequired: step.type === StepType.APPROVAL_GATE
    };

    // Update execution with running step
    await this.updateStepResult(execution.id, step.id, stepResult);

    try {
      let outputs: Record<string, any> = {};

      // Debug logging
      console.log(`🔧 Executing step ${step.id} of type ${step.type} in execution ${execution.id}`);

      switch (step.type) {
        case StepType.TEXT_INPUT:
          outputs = await this.executeTextInput(step, stepResult.inputs);
          break;
        case StepType.AI_GENERATION:
          outputs = await this.executeAIGeneration(step, stepResult.inputs, execution.id);
          break;
        case StepType.HUMAN_REVIEW:
          console.log(`📝 Creating human review for step ${step.id}`);
          outputs = await this.executeHumanReview(execution, step, stepResult.inputs);
          break;
        case StepType.APPROVAL_GATE:
          console.log(`✅ Creating approval gate for step ${step.id}`);
          outputs = await this.executeApprovalGate(execution, step, stepResult.inputs);
          break;
        default:
          throw new Error(`Step type ${step.type} not implemented`);
      }

      // For approval gates, don't mark as completed - they should stay in WAITING_APPROVAL
      if (step.type === StepType.APPROVAL_GATE) {
        console.log(`⏸️ Approval gate ${step.id} created, staying in WAITING_APPROVAL status`);

        // Update step result to waiting for approval
        const waitingResult: StepResult = {
          ...stepResult,
          status: StepStatus.WAITING_APPROVAL,
          outputs,
          artifactId: outputs.artifact_id,
          approvalRequired: true,
          completedAt: undefined // Not completed yet
        };

        await this.updateStepResult(execution.id, step.id, waitingResult);
        return waitingResult;
      }

      // Mark regular steps as completed
      const completedResult: StepResult = {
        ...stepResult,
        status: StepStatus.COMPLETED,
        outputs,
        completedAt: new Date().toISOString(),
        duration: Date.now() - new Date(startTime).getTime()
      };

      await this.updateStepResult(execution.id, step.id, completedResult);
      return completedResult;

    } catch (error) {
      const failedResult: StepResult = {
        ...stepResult,
        status: StepStatus.FAILED,
        error: error instanceof Error ? error.message : String(error),
        completedAt: new Date().toISOString(),
        duration: Date.now() - new Date(startTime).getTime()
      };

      await this.updateStepResult(execution.id, step.id, failedResult);
      throw error;
    }
  }

  private buildStepInputs(execution: WorkflowExecution, step: WorkflowStep): Record<string, any> {
    const context: VariableContext = {
      ...execution.inputs
    };

    // Add outputs from previous steps
    for (const [stepId, result] of Object.entries(execution.stepResults)) {
      if (result.status === StepStatus.COMPLETED) {
        Object.assign(context, result.outputs);
      }
    }

    // Extract only the inputs this step needs
    const stepInputs: Record<string, any> = {};
    for (const inputName of step.inputs) {
      if (context[inputName] !== undefined) {
        stepInputs[inputName] = context[inputName];
      }
    }

    return stepInputs;
  }

  private async executeTextInput(step: WorkflowStep, inputs: Record<string, any>): Promise<Record<string, any>> {
    // For text input steps, the inputs are already provided in the execution inputs
    // Just pass them through as outputs
    const outputs: Record<string, any> = {};

    for (const outputName of step.outputs) {
      if (inputs[outputName] !== undefined) {
        outputs[outputName] = inputs[outputName];
      }
    }

    return outputs;
  }

  private async executeAIGeneration(step: WorkflowStep, inputs: Record<string, any>, executionId?: string): Promise<Record<string, any>> {
    // Check if agent consultation is enabled for this step
    if (step.consultationConfig?.enabled) {
      console.log(`🤖 Using enhanced AI generation with agent consultation for step ${step.id}`);
      return await this.executeEnhancedAIGeneration(step, inputs, executionId || '');
    }

    // Fall back to regular AI generation
    console.log(`🤖 Using regular AI generation for step ${step.id}`);
    return await this.executeRegularAIGeneration(step, inputs, executionId);
  }

  private async executeEnhancedAIGeneration(step: WorkflowStep, inputs: Record<string, any>, executionId: string): Promise<Record<string, any>> {
    try {
      console.log(`🚀 Executing enhanced AI generation with agent consultation for step ${step.id}`);

      // Use the enhanced AI generation step
      const result = await this.enhancedAIStep.executeAIGenerationWithConsultation(
        step,
        inputs,
        executionId
      );

      // Create content item for the enhanced content
      const contentId = uuidv4();
      const contentItem: ContentItem = {
        id: contentId,
        type: this.inferContentType(step.type),
        title: step.name,
        content: result.outputs.content || result.outputs,
        status: ContentStatus.DRAFT,
        executionId,
        stepId: step.id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        metadata: {
          enhancedWithAgents: true,
          consultationSummary: result.outputs.consultationSummary,
          agentInsights: result.outputs.agentInsights,
          consultationResults: result.consultationResults?.length || 0
        }
      };

      await this.stateStore.setContent(contentItem);

      // Return enhanced outputs
      const outputs: Record<string, any> = {
        content_id: contentId,
        ...result.outputs
      };

      // Map to step outputs
      for (const outputName of step.outputs) {
        if (outputName === 'content' || outputName.includes('content')) {
          outputs[outputName] = result.outputs.content || result.outputs[outputName];
        } else if (outputName.includes('id')) {
          outputs[outputName] = contentId;
        }
      }

      console.log(`✅ Enhanced AI generation completed for step ${step.id} with ${result.consultationResults?.length || 0} consultations`);
      return outputs;

    } catch (error) {
      console.error(`❌ Enhanced AI generation failed for step ${step.id}:`, error);

      // Fall back to regular AI generation if consultation fails and fallback is 'continue'
      if (step.consultationConfig?.fallbackBehavior === 'continue') {
        console.log(`🔄 Falling back to regular AI generation for step ${step.id}`);
        return await this.executeRegularAIGeneration(step, inputs, executionId);
      }

      throw error;
    }
  }

  private async executeRegularAIGeneration(step: WorkflowStep, inputs: Record<string, any>, executionId?: string): Promise<Record<string, any>> {
    const aiConfig = step.config.aiConfig;
    if (!aiConfig) {
      throw new Error('AI configuration not found for AI generation step');
    }

    // Replace variables in prompt
    let prompt = aiConfig.prompt;
    for (const [key, value] of Object.entries(inputs)) {
      prompt = prompt.replace(new RegExp(`{{${key}}}`, 'g'), String(value));
    }

    // Generate content using AI
    const result = await this.aiManager.generate(prompt, {
      provider: aiConfig.provider,
      model: aiConfig.model,
      temperature: aiConfig.temperature,
      maxTokens: aiConfig.maxTokens,
      systemPrompt: aiConfig.systemPrompt,
      userApiKey: aiConfig.userApiKey
    });

    // Create content item
    const contentId = uuidv4();
    const contentItem: ContentItem = {
      id: contentId,
      type: this.inferContentType(step.type),
      title: step.name,
      content: result.content,
      status: ContentStatus.DRAFT,
      executionId: executionId || '',
      stepId: step.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      metadata: {
        aiModel: result.model,
        aiProvider: result.provider,
        cost: result.cost,
        wordCount: result.content.split(' ').length,
        enhancedWithAgents: false
      }
    };

    await this.stateStore.setContent(contentItem);

    // Return outputs
    const outputs: Record<string, any> = {
      content_id: contentId,
      content: result.content
    };

    // Map to step outputs
    for (const outputName of step.outputs) {
      if (outputName === 'content' || outputName.includes('content')) {
        outputs[outputName] = result.content;
      } else if (outputName.includes('id')) {
        outputs[outputName] = contentId;
      }
    }

    return outputs;
  }

  private async executeHumanReview(
    execution: WorkflowExecution,
    step: WorkflowStep,
    inputs: Record<string, any>
  ): Promise<Record<string, any>> {
    console.log(`👥 Executing human review: ${step.name} (${step.id})`);
    console.log(`📋 Inputs:`, Object.keys(inputs));

    const reviewConfig = step.config.reviewConfig;
    if (!reviewConfig) {
      throw new Error('Review configuration not found for human review step');
    }

    // Find content to review
    let contentToReview = '';
    let contentId = '';

    for (const [key, value] of Object.entries(inputs)) {
      if (key.includes('content') && typeof value === 'string') {
        contentToReview = value;
      }
      if (key.includes('id') || key.includes('content_id')) {
        contentId = String(value);
      }
    }

    if (!contentToReview) {
      throw new Error('No content found to review');
    }

    // Create review
    console.log(`🔍 Creating review with content length: ${contentToReview.length}`);
    const reviewLink = await this.reviewSystem.createReview(contentToReview, {
      contentId: contentId || uuidv4(),
      executionId: execution.id,
      stepId: step.id,
      type: reviewConfig.reviewType as any,
      instructions: reviewConfig.instructions,
      deadline: reviewConfig.deadline,
      reviewers: reviewConfig.reviewers
    });
    console.log(`📝 Review created with ID: ${reviewLink.reviewId}`);

    // Return review info (step will be completed when review is submitted)
    return {
      review_id: reviewLink.reviewId,
      review_url: reviewLink.url,
      status: 'waiting_review'
    };
  }

  private async executeApprovalGate(
    execution: WorkflowExecution,
    step: WorkflowStep,
    inputs: Record<string, any>
  ): Promise<Record<string, any>> {
    console.log(`🚪 Executing approval gate: ${step.name} (${step.id})`);
    console.log(`📋 Inputs:`, Object.keys(inputs));
    console.log(`📋 Input values:`, inputs);

    // Determine artifact type based on step name or inputs
    let artifactType: ArtifactType = ArtifactType.CONTENT_DRAFT;

    if (step.name.toLowerCase().includes('keyword')) {
      artifactType = ArtifactType.KEYWORD_RESEARCH;
    } else if (step.name.toLowerCase().includes('content')) {
      artifactType = ArtifactType.CONTENT_DRAFT;
    } else if (step.name.toLowerCase().includes('seo')) {
      artifactType = ArtifactType.SEO_OPTIMIZATION;
    }

    console.log(`📄 Artifact type determined: ${artifactType}`);

    // Get content from previous steps for approval
    let contentToApprove = inputs;

    // If inputs are empty, try to get content from dependent steps
    if (Object.keys(inputs).length === 0 && step.dependencies.length > 0) {
      console.log(`📋 No direct inputs, collecting content from dependencies: ${step.dependencies}`);
      for (const depStepId of step.dependencies) {
        const depResult = execution.stepResults[depStepId];
        if (depResult && depResult.outputs) {
          Object.assign(contentToApprove, depResult.outputs);
          console.log(`📋 Added content from step ${depStepId}:`, Object.keys(depResult.outputs));
        }
      }
    }

    console.log(`🏗️ Creating artifact for approval gate with content:`, Object.keys(contentToApprove));
    const artifactId = await this.createArtifact(execution.id, step.id, {
      stepId: step.id,
      executionId: execution.id,
      type: artifactType,
      title: `${step.name} - Approval Required`,
      content: contentToApprove,
      status: ArtifactStatus.PENDING_APPROVAL,
      version: 1,
      createdBy: execution.metadata.userId || 'system'
    });
    console.log(`✅ Artifact created with ID: ${artifactId}`);

    console.log(`🎯 Approval gate created successfully. Workflow will pause until artifact ${artifactId} is approved.`);

    // Return the artifact ID and approval URL
    return {
      artifact_id: artifactId,
      approval_url: `/workflow/approval/${artifactId}`,
      status: 'waiting_approval',
      instructions: step.config.reviewConfig?.instructions || 'Please review and approve this artifact to continue the workflow.'
    };
  }

  private inferContentType(stepType: StepType): ContentType {
    switch (stepType) {
      case StepType.AI_GENERATION:
        return ContentType.GENERIC;
      case StepType.KEYWORD_RESEARCH:
        return ContentType.KEYWORD_RESEARCH;
      case StepType.CONTENT_CREATION:
        return ContentType.BLOG_POST;
      case StepType.SEO_OPTIMIZATION:
        return ContentType.SEO_ANALYSIS;
      default:
        return ContentType.GENERIC;
    }
  }

  private mapArtifactTypeToContentType(artifactType: ArtifactType): ContentType {
    switch (artifactType) {
      case ArtifactType.KEYWORD_RESEARCH:
        return ContentType.KEYWORD_RESEARCH;
      case ArtifactType.CONTENT_STRATEGY:
        return ContentType.GENERIC;
      case ArtifactType.CONTENT_DRAFT:
        return ContentType.BLOG_POST;
      case ArtifactType.SEO_OPTIMIZATION:
        return ContentType.SEO_ANALYSIS;
      case ArtifactType.FINAL_CONTENT:
        return ContentType.BLOG_POST;
      case ArtifactType.REVIEW_FEEDBACK:
        return ContentType.GENERIC;
      default:
        return ContentType.GENERIC;
    }
  }

  /**
   * Register an approval gate for a workflow
   */
  async registerApprovalGate(gate: ApprovalGate): Promise<void> {
    try {
      // Store approval gate in state
      await this.stateStore.update(state => {
        if (!state) return null;

        return {
          ...state,
          approvalGates: {
            ...state.approvalGates,
            [gate.id]: gate
          }
        };
      });

      console.log(`✅ Approval gate ${gate.id} registered for step ${gate.stepId}`);
    } catch (error) {
      console.error(`Failed to register approval gate ${gate.id}:`, error);
      throw error;
    }
  }

  private async updateStepResult(executionId: string, stepId: string, stepResult: StepResult): Promise<void> {
    const execution = await this.getExecution(executionId);
    if (!execution) {
      throw new Error(`Execution ${executionId} not found`);
    }

    const updatedExecution: WorkflowExecution = {
      ...execution,
      stepResults: {
        ...execution.stepResults,
        [stepId]: stepResult
      },
      currentStep: stepResult.status === StepStatus.RUNNING ? stepId : execution.currentStep,
      progress: this.calculateProgress(execution.stepResults, stepId, stepResult)
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  private calculateProgress(stepResults: Record<string, StepResult>, currentStepId: string, currentResult: StepResult): number {
    const allResults = { ...stepResults, [currentStepId]: currentResult };
    const totalSteps = Object.keys(allResults).length;
    const completedSteps = Object.values(allResults).filter(r => r.status === StepStatus.COMPLETED).length;

    return Math.round((completedSteps / totalSteps) * 100);
  }

  private async markExecutionCompleted(executionId: string): Promise<void> {
    const execution = await this.getExecution(executionId);
    if (!execution) return;

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.COMPLETED,
      progress: 100,
      completedAt: new Date().toISOString()
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  private async markExecutionFailed(executionId: string, error: any): Promise<void> {
    const execution = await this.getExecution(executionId);
    if (!execution) return;

    const updatedExecution: WorkflowExecution = {
      ...execution,
      status: ExecutionStatus.FAILED,
      error: {
        message: error instanceof Error ? error.message : String(error),
        code: 'EXECUTION_FAILED',
        recoverable: true,
        timestamp: new Date().toISOString()
      },
      completedAt: new Date().toISOString()
    };

    await this.stateStore.setExecution(updatedExecution);
  }

  private async markStepFailed(executionId: string, stepId: string, error: any): Promise<void> {
    const stepResult: StepResult = {
      stepId,
      status: StepStatus.FAILED,
      inputs: {},
      outputs: {},
      startedAt: new Date().toISOString(),
      completedAt: new Date().toISOString(),
      error: error instanceof Error ? error.message : String(error)
    };

    await this.updateStepResult(executionId, stepId, stepResult);
  }

  /**
   * Get execution metrics including agent consultation data
   */
  async getExecutionMetrics(executionId: string): Promise<{
    execution: WorkflowExecution | null;
    consultationMetrics?: any;
    agentHealth?: any;
  }> {
    const execution = await this.getExecution(executionId);

    if (!execution) {
      return { execution: null };
    }

    // Get consultation metrics from the enhanced AI step
    const consultationMetrics = this.enhancedAIStep.getConsultationMetrics();
    const agentHealth = await this.enhancedAIStep.performHealthCheck();

    return {
      execution,
      consultationMetrics,
      agentHealth
    };
  }

  /**
   * Get agent consultation status for monitoring
   */
  async getAgentConsultationStatus(): Promise<{
    metrics: any;
    agentStatus: any[];
    healthCheck: any;
  }> {
    const metrics = this.enhancedAIStep.getConsultationMetrics();
    const agentStatus = await this.enhancedAIStep.getAgentStatus();
    const healthCheck = await this.enhancedAIStep.performHealthCheck();

    return {
      metrics,
      agentStatus,
      healthCheck
    };
  }
}


